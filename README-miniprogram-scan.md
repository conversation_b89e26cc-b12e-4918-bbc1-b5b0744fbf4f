# 小程序 webview 扫码功能使用指南

本项目实现了在小程序内嵌网页中使用扫码功能，通过 `wx.miniProgram.postMessage` 与小程序通信。

## 🚀 快速开始

### 1. 网页端使用

在您的 Vue 组件中使用 `QrScanner` 组件：

```vue
<template>
  <QrScanner
    button-text="扫一扫"
    button-type="primary"
    :on-success="handleScanSuccess"
    :on-error="handleScanError"
  />
</template>

<script setup>
import QrScanner from '@/components/QrScanner/index.vue'

function handleScanSuccess(result) {
  console.log('扫码成功:', result)
  // 处理扫码结果
}

function handleScanError(error) {
  console.error('扫码失败:', error)
  // 处理扫码错误
}
</script>
```

### 2. 直接调用智能扫码 API

```javascript
import { smartScan } from '@/utils/miniProgramScan'

async function doScan() {
  try {
    const result = await smartScan({
      timeout: 30000,
      retryCount: 2,
      onProgress: (message) => {
        console.log('进度:', message)
      },
      onSuccess: (scanResult) => {
        console.log('扫码成功:', scanResult)
      },
      onError: (error) => {
        console.error('扫码失败:', error)
      }
    })
    
    if (result.success) {
      console.log('最终结果:', result.result)
    }
  } catch (error) {
    console.error('扫码异常:', error)
  }
}
```

## 🔧 小程序端配置

### 方案一：使用提供的完整示例

1. 复制 `examples/miniprogram-webview-scan.vue` 到您的小程序项目
2. 修改 `baseUrl` 为您的网页地址
3. 在 `pages.json` 中注册页面

### 方案二：自定义实现

参考 `docs/miniprogram-scan-setup.md` 中的详细配置说明。

## 📱 功能特性

### 智能扫码策略

1. **环境检测**：自动检测是否在小程序 webview 环境
2. **多重通信**：支持 `postMessage` 和 URL 参数两种通信方式
3. **自动重试**：支持配置重试次数和延迟
4. **超时处理**：可配置扫码超时时间
5. **进度回调**：实时反馈扫码进度

### 通信协议

#### 网页 → 小程序
```javascript
{
  type: 'requestScan',
  timestamp: 1234567890,
  requestId: 'abc123def'
}
```

#### 小程序 → 网页
```javascript
// 成功
{
  type: 'scanResult',
  success: true,
  result: '扫码内容',
  timestamp: 1234567890,
  requestId: 'abc123def'
}

// 失败
{
  type: 'scanResult',
  success: false,
  error: '错误信息',
  timestamp: 1234567890,
  requestId: 'abc123def'
}

// 取消
{
  type: 'scanCancel',
  timestamp: 1234567890,
  requestId: 'abc123def'
}
```

## 🛠️ API 参考

### smartScan(config)

智能扫码函数，自动选择最佳扫码方式。

**参数：**
- `config.timeout` (number): 超时时间，默认 30000ms
- `config.retryCount` (number): 重试次数，默认 2 次
- `config.retryDelay` (number): 重试延迟，默认 1000ms
- `config.onSuccess` (function): 成功回调
- `config.onError` (function): 失败回调
- `config.onCancel` (function): 取消回调
- `config.onProgress` (function): 进度回调

**返回值：**
```javascript
{
  success: boolean,
  result?: string,
  error?: string
}
```

### 环境检测函数

```javascript
import { 
  isInMiniProgramWebview,
  canUseMiniProgramScan 
} from '@/utils/miniProgramScan'

// 检查是否在小程序环境
if (isInMiniProgramWebview()) {
  console.log('当前在小程序 webview 中')
}

// 检查是否支持扫码
if (canUseMiniProgramScan()) {
  console.log('支持小程序扫码')
}
```

## 🧪 测试和调试

### 测试页面

访问 `/test-scan` 页面进行功能测试：

1. **环境检测**：查看当前环境信息
2. **扫码测试**：测试不同的扫码方式
3. **通信测试**：测试与小程序的消息通信
4. **调试日志**：查看详细的调试信息

### 调试技巧

1. **开启调试模式**：在小程序开发者工具中查看 console 日志
2. **检查网络**：确保网页域名已配置为小程序业务域名
3. **验证权限**：确保小程序有相机权限
4. **测试通信**：使用测试页面验证消息通信是否正常

## ⚠️ 重要说明

### 限制和注意事项

1. **单向通信**：小程序无法直接向 webview 发送消息
2. **域名配置**：网页域名必须在小程序后台配置为业务域名
3. **HTTPS 要求**：网页必须使用 HTTPS 协议
4. **权限申请**：小程序需要申请相机权限

### 解决方案

本项目通过以下方式解决通信限制：

1. **URL 参数传递**：小程序通过修改 webview URL 传递扫码结果
2. **自动检测**：网页端自动检测 URL 变化获取结果
3. **双重保障**：同时支持 postMessage 和 URL 参数两种方式

## 📚 相关文档

- [详细配置指南](docs/miniprogram-scan-setup.md)
- [小程序端示例](examples/miniprogram-webview-scan.vue)
- [uni-app 示例](docs/uniapp-webview-example.vue)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个功能。

## 📄 许可证

MIT License
