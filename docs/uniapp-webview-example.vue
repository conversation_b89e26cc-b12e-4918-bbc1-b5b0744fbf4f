<!-- uniapp 小程序 webview 页面示例 -->
<template>
  <view class="webview-page">
    <!-- 顶部导航栏（可选） -->
    <view class="nav-bar" v-if="showNavBar">
      <view class="nav-title">{{ pageTitle }}</view>
    </view>
    
    <!-- webview 容器 -->
    <view class="webview-container">
      <web-view 
        ref="webview"
        :src="webviewUrl" 
        @message="onWebviewMessage"
        @load="onWebviewLoad"
        @error="onWebviewError">
      </web-view>
    </view>
    
    <!-- 加载提示 -->
    <view class="loading-mask" v-if="loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // webview URL
      webviewUrl: 'https://your-domain.com/your-page',
      // 页面标题
      pageTitle: '应用',
      // 是否显示导航栏
      showNavBar: false,
      // 加载状态
      loading: true,
      // 消息队列（用于调试）
      messageQueue: []
    }
  },

  onLoad(options) {
    console.log('页面加载参数:', options)
    
    // 从参数中获取 webview URL
    if (options.url) {
      this.webviewUrl = decodeURIComponent(options.url)
    }
    
    // 从参数中获取页面标题
    if (options.title) {
      this.pageTitle = decodeURIComponent(options.title)
      uni.setNavigationBarTitle({
        title: this.pageTitle
      })
    }
    
    // 初始化消息监听
    this.initMessageListener()
  },

  onShow() {
    console.log('页面显示')
  },

  onHide() {
    console.log('页面隐藏')
  },

  methods: {
    // 初始化消息监听
    initMessageListener() {
      console.log('初始化 webview 消息监听...')
      
      // #ifdef MP-WEIXIN
      // 监听全局 webview 消息（备用方案）
      if (wx.onMessage) {
        wx.onMessage((data) => {
          console.log('收到 webview 全局消息:', data)
          this.handleWebviewMessage(data)
        })
      }
      // #endif
    },

    // webview 加载完成
    onWebviewLoad(event) {
      console.log('webview 加载完成:', event)
      this.loading = false
    },

    // webview 加载错误
    onWebviewError(event) {
      console.error('webview 加载错误:', event)
      this.loading = false
      
      uni.showToast({
        title: '页面加载失败',
        icon: 'none',
        duration: 2000
      })
    },

    // webview 消息事件处理
    onWebviewMessage(event) {
      console.log('webview 消息事件:', event.detail.data)
      
      // event.detail.data 是一个数组，包含所有消息
      if (event.detail.data && event.detail.data.length > 0) {
        // 处理所有消息
        event.detail.data.forEach(data => {
          this.handleWebviewMessage(data)
        })
      }
    },

    // 处理 webview 消息
    handleWebviewMessage(data) {
      console.log('处理 webview 消息:', data)
      
      // 添加到消息队列（用于调试）
      this.messageQueue.push({
        timestamp: new Date().toLocaleTimeString(),
        data: data
      })
      
      // 根据消息类型处理
      switch (data.type) {
        case 'requestScan':
          this.handleScanRequest(data)
          break
        case 'test':
          this.handleTestMessage(data)
          break
        case 'navigate':
          this.handleNavigateRequest(data)
          break
        default:
          console.log('未知消息类型:', data.type)
      }
    },

    // 处理扫码请求
    handleScanRequest(data) {
      console.log('开始扫码...', data)
      
      uni.scanCode({
        // 扫码类型
        scanType: ['barCode', 'qrCode'],
        // 是否只能从相机扫码
        onlyFromCamera: true,
        success: (res) => {
          console.log('扫码成功:', res)
          
          this.sendMessageToWebview({
            type: 'scanResult',
            success: true,
            result: res.result,
            scanType: res.scanType,
            charSet: res.charSet,
            path: res.path,
            timestamp: Date.now()
          })
          
          // 显示成功提示
          uni.showToast({
            title: '扫码成功',
            icon: 'success',
            duration: 1500
          })
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          
          let errorMessage = '扫码失败'
          if (error.errMsg) {
            if (error.errMsg.includes('cancel')) {
              errorMessage = '用户取消扫码'
            } else if (error.errMsg.includes('permission')) {
              errorMessage = '没有相机权限'
            } else {
              errorMessage = error.errMsg
            }
          }
          
          this.sendMessageToWebview({
            type: 'scanResult',
            success: false,
            error: errorMessage,
            timestamp: Date.now()
          })
          
          // 显示错误提示
          if (!errorMessage.includes('取消')) {
            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 2000
            })
          }
        }
      })
    },

    // 处理测试消息
    handleTestMessage(data) {
      console.log('收到测试消息:', data.message)
      
      // 回复测试消息
      this.sendMessageToWebview({
        type: 'testResponse',
        message: '小程序收到测试消息',
        originalMessage: data.message,
        timestamp: Date.now()
      })
    },

    // 处理导航请求
    handleNavigateRequest(data) {
      console.log('处理导航请求:', data)
      
      if (data.action === 'back') {
        uni.navigateBack()
      } else if (data.action === 'close') {
        uni.navigateBack()
      } else if (data.url) {
        uni.navigateTo({
          url: data.url
        })
      }
    },

    // 发送消息到 webview
    sendMessageToWebview(data) {
      console.log('发送消息到 webview:', data)
      
      const webview = this.$refs.webview
      if (webview) {
        // #ifdef MP-WEIXIN
        try {
          webview.postMessage({ data })
          console.log('消息发送成功')
        } catch (error) {
          console.error('消息发送失败:', error)
        }
        // #endif
      } else {
        console.error('未找到 webview 组件')
      }
    },

    // 手动触发扫码（用于测试）
    manualScan() {
      this.handleScanRequest({ type: 'requestScan' })
    }
  }
}
</script>

<style scoped>
.webview-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  height: 44px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #e5e5e5;
}

.nav-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.webview-container {
  flex: 1;
  width: 100%;
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
