# 小程序内嵌网页扫码功能实现指南

本指南介绍如何在小程序内嵌网页(web-view)中实现扫码功能，无需公众号配置。

## 功能特点

- ✅ **无需公众号**：只需要小程序账号即可
- ✅ **无需后端签名**：不需要复杂的wx.config配置
- ✅ **自动环境检测**：自动识别小程序环境和普通网页环境
- ✅ **统一API接口**：网页端使用相同的扫码API
- ✅ **完整错误处理**：包含超时、取消等各种异常情况

## 实现原理

### 传统方案 vs 小程序方案

| 特性 | 传统公众号方案 | 小程序内嵌网页方案 |
|------|----------------|-------------------|
| 账号要求 | 需要认证公众号 | 只需小程序账号 |
| 后端配置 | 需要签名验证 | 无需后端配置 |
| 域名配置 | 需要JS安全域名 | 需要业务域名 |
| API调用 | wx.scanQRCode | wx.miniProgram.postMessage |
| 权限验证 | wx.config | 自动获得权限 |

### 通信流程

```mermaid
sequenceDiagram
    participant W as 网页
    participant MP as 小程序
    participant API as 微信API
    
    W->>W: 检测环境(wx.miniProgram.getEnv)
    W->>MP: 发送扫码请求(postMessage)
    MP->>API: 调用扫码API(wx.scanCode)
    API->>MP: 返回扫码结果
    MP->>W: 发送扫码结果(storage/message)
    W->>W: 处理扫码结果
```

## 快速开始

### 1. 网页端配置

您的网页已经包含了自动环境检测功能，无需额外配置：

```vue
<template>
  <QrScanner 
    button-text="扫一扫"
    :on-success="handleScanSuccess"
    :on-error="handleScanError"
  />
</template>

<script setup>
function handleScanSuccess(result, parsedResult) {
  console.log('扫码成功:', result)
  // 处理扫码结果
}

function handleScanError(error) {
  console.error('扫码失败:', error)
  // 处理扫码错误
}
</script>
```

### 2. 小程序端配置

#### 2.1 页面文件结构

```
pages/webview/
├── index.js     # 页面逻辑
├── index.wxml   # 页面模板
├── index.wxss   # 页面样式
└── index.json   # 页面配置
```

#### 2.2 页面配置 (index.json)

```json
{
  "navigationBarTitleText": "网页应用",
  "enablePullDownRefresh": false
}
```

#### 2.3 页面逻辑 (index.js)

```javascript
Page({
  data: {
    webviewUrl: 'https://your-domain.com/your-page'
  },

  onMessage(e) {
    const messages = e.detail.data
    if (!Array.isArray(messages) || messages.length === 0) return
    
    const latestMessage = messages[messages.length - 1]
    this.handleWebviewMessage(latestMessage)
  },

  handleWebviewMessage(message) {
    if (message.type === 'requestScan') {
      this.handleScanRequest(message.config || {})
    }
  },

  handleScanRequest(config = {}) {
    wx.scanCode({
      onlyFromCamera: config.onlyFromCamera !== false,
      scanType: config.scanType || ['qrCode', 'barCode'],
      success: (res) => {
        // 将结果存储到本地，供网页读取
        wx.setStorageSync('webview_scan_result', {
          type: 'scanResult',
          success: true,
          result: res.result,
          scanType: res.scanType,
          timestamp: Date.now()
        })
      },
      fail: (error) => {
        wx.setStorageSync('webview_scan_result', {
          type: 'scanResult',
          success: false,
          error: error.errMsg || '扫码失败',
          timestamp: Date.now()
        })
      }
    })
  }
})
```

#### 2.4 页面模板 (index.wxml)

```xml
<web-view 
  src="{{webviewUrl}}" 
  bindmessage="onMessage"
></web-view>
```

### 3. 小程序配置

#### 3.1 app.json 配置

```json
{
  "pages": [
    "pages/webview/index"
  ],
  "permission": {
    "scope.camera": {
      "desc": "用于扫描二维码"
    }
  }
}
```

#### 3.2 业务域名配置

在小程序管理后台配置业务域名：
1. 登录小程序管理后台
2. 进入"开发" -> "开发管理" -> "开发设置"
3. 在"业务域名"中添加您的网页域名

## 高级功能

### 1. 扫码类型配置

```javascript
// 仅扫描二维码
const config = {
  scanType: ['qrCode']
}

// 仅扫描条形码
const config = {
  scanType: ['barCode']
}

// 扫描所有类型
const config = {
  scanType: ['qrCode', 'barCode', 'datamatrix', 'pdf417']
}
```

### 2. 错误处理

```javascript
function handleScanError(error) {
  switch (error.message) {
    case '用户取消':
      // 用户主动取消扫码
      break
    case '扫码超时':
      // 30秒超时
      break
    case '小程序环境不可用':
      // 不在小程序环境中
      break
    default:
      // 其他错误
      console.error('扫码失败:', error)
  }
}
```

### 3. 环境检测

```javascript
import { wechatSDK } from '@/utils/wechat'

// 检测是否在小程序环境中
const isInMiniProgram = await wechatSDK.isMiniProgram()

// 检测是否支持扫码
const canScan = await wechatSDK.canScan()
```

## 常见问题

### Q1: 为什么扫码没有反应？

**可能原因：**
1. 小程序没有获得摄像头权限
2. 业务域名配置错误
3. 网页和小程序通信异常

**解决方案：**
1. 检查小程序权限配置
2. 确认业务域名已正确配置
3. 查看控制台错误信息

### Q2: 如何调试通信问题？

**调试步骤：**
1. 在网页中打开控制台查看日志
2. 在小程序开发者工具中查看日志
3. 检查 `wx.setStorageSync` 和 `wx.getStorageSync` 的数据

### Q3: 扫码结果如何传回网页？

**当前实现：**
- 小程序将结果存储到本地存储
- 网页通过轮询检查存储数据
- 支持超时和错误处理

## 注意事项

1. **权限申请**：确保小程序已获得摄像头权限
2. **域名配置**：网页域名必须在小程序业务域名白名单中
3. **HTTPS要求**：网页必须使用HTTPS协议
4. **兼容性**：建议同时支持小程序和公众号两种模式

## 更新日志

- **v1.0.0**: 初始版本，支持基本扫码功能
- **v1.1.0**: 添加自动环境检测
- **v1.2.0**: 完善错误处理和超时机制
