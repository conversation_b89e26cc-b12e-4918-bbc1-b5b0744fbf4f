# 小程序 webview 扫码功能配置指南

本文档介绍如何在小程序端配置 webview 扫码功能，使内嵌的网页能够通过 `wx.miniProgram.postMessage` 与小程序通信实现扫码。

## 功能原理

1. **网页端**：通过 `wx.miniProgram.postMessage` 发送扫码请求
2. **小程序端**：监听 webview 消息，调用小程序扫码 API
3. **小程序端**：将扫码结果通过 `postMessage` 返回给网页

## 消息协议

### 网页发送给小程序的消息

```javascript
{
  type: 'requestScan',
  timestamp: 1234567890,
  requestId: 'abc123def'
}
```

### 小程序返回给网页的消息

**扫码成功：**
```javascript
{
  type: 'scanResult',
  success: true,
  result: '扫码结果内容',
  timestamp: 1234567890,
  requestId: 'abc123def'
}
```

**扫码失败：**
```javascript
{
  type: 'scanResult',
  success: false,
  error: '错误信息',
  timestamp: 1234567890,
  requestId: 'abc123def'
}
```

**用户取消：**
```javascript
{
  type: 'scanCancel',
  timestamp: 1234567890,
  requestId: 'abc123def'
}
```

## 小程序端配置

### 1. 原生微信小程序

#### 页面模板 (webview.wxml)
```xml
<web-view 
  id="webview" 
  src="{{webviewUrl}}" 
  bindmessage="onWebviewMessage"
  bindload="onWebviewLoad"
  binderror="onWebviewError">
</web-view>
```

#### 页面逻辑 (webview.js)
```javascript
Page({
  data: {
    webviewUrl: 'https://your-domain.com/your-page'
  },

  onLoad(options) {
    console.log('webview 页面加载', options)
    
    // 从参数获取 URL
    if (options.url) {
      this.setData({
        webviewUrl: decodeURIComponent(options.url)
      })
    }
  },

  // webview 加载完成
  onWebviewLoad(e) {
    console.log('webview 加载完成', e)
  },

  // webview 加载错误
  onWebviewError(e) {
    console.error('webview 加载错误', e)
    wx.showToast({
      title: '页面加载失败',
      icon: 'none'
    })
  },

  // 监听 webview 消息
  onWebviewMessage(e) {
    console.log('收到 webview 消息', e.detail.data)
    
    if (e.detail.data && e.detail.data.length > 0) {
      // 获取最新的消息
      const latestData = e.detail.data[e.detail.data.length - 1]
      this.handleWebviewMessage(latestData)
    }
  },

  // 处理 webview 消息
  handleWebviewMessage(data) {
    console.log('处理消息', data)
    
    if (data.type === 'requestScan') {
      this.handleScanRequest(data)
    }
  },

  // 处理扫码请求
  handleScanRequest(requestData) {
    console.log('开始扫码', requestData)
    
    wx.scanCode({
      success: (res) => {
        console.log('扫码成功', res)
        this.sendMessageToWebview({
          type: 'scanResult',
          success: true,
          result: res.result,
          timestamp: Date.now(),
          requestId: requestData.requestId
        })
      },
      fail: (error) => {
        console.error('扫码失败', error)
        
        let errorMessage = '扫码失败'
        if (error.errMsg) {
          if (error.errMsg.includes('cancel')) {
            // 用户取消
            this.sendMessageToWebview({
              type: 'scanCancel',
              timestamp: Date.now(),
              requestId: requestData.requestId
            })
            return
          } else if (error.errMsg.includes('permission')) {
            errorMessage = '没有相机权限'
          } else {
            errorMessage = error.errMsg
          }
        }
        
        this.sendMessageToWebview({
          type: 'scanResult',
          success: false,
          error: errorMessage,
          timestamp: Date.now(),
          requestId: requestData.requestId
        })
      }
    })
  },

  // 发送消息到 webview
  sendMessageToWebview(data) {
    console.log('发送消息到 webview', data)
    
    // 注意：小程序无法直接向 webview 发送消息
    // 这里只是示例，实际需要通过其他方式实现
    // 比如通过 URL 参数、localStorage 等方式
  }
})
```

### 2. uni-app 小程序

#### 页面模板 (webview.vue)
```vue
<template>
  <view class="webview-container">
    <web-view 
      ref="webview"
      :src="webviewUrl" 
      @message="onWebviewMessage"
      @load="onWebviewLoad"
      @error="onWebviewError">
    </web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      webviewUrl: 'https://your-domain.com/your-page'
    }
  },

  onLoad(options) {
    console.log('webview 页面加载', options)
    
    if (options.url) {
      this.webviewUrl = decodeURIComponent(options.url)
    }
  },

  methods: {
    // webview 加载完成
    onWebviewLoad(e) {
      console.log('webview 加载完成', e)
    },

    // webview 加载错误
    onWebviewError(e) {
      console.error('webview 加载错误', e)
      uni.showToast({
        title: '页面加载失败',
        icon: 'none'
      })
    },

    // 监听 webview 消息
    onWebviewMessage(e) {
      console.log('收到 webview 消息', e.detail.data)
      
      if (e.detail.data && e.detail.data.length > 0) {
        const latestData = e.detail.data[e.detail.data.length - 1]
        this.handleWebviewMessage(latestData)
      }
    },

    // 处理 webview 消息
    handleWebviewMessage(data) {
      console.log('处理消息', data)
      
      if (data.type === 'requestScan') {
        this.handleScanRequest(data)
      }
    },

    // 处理扫码请求
    handleScanRequest(requestData) {
      console.log('开始扫码', requestData)
      
      uni.scanCode({
        success: (res) => {
          console.log('扫码成功', res)
          // 注意：uni-app 中无法直接向 webview 发送消息
          // 需要通过其他方式实现，如 URL 参数、本地存储等
        },
        fail: (error) => {
          console.error('扫码失败', error)
        }
      })
    }
  }
}
</script>
```

## 重要说明

⚠️ **关键限制**：小程序无法直接向 webview 发送消息！

小程序的 webview 组件是单向通信的：
- ✅ webview 可以向小程序发送消息（通过 `postMessage`）
- ❌ 小程序无法直接向 webview 发送消息

## 解决方案

### 方案1：URL 参数传递（推荐）

1. 扫码成功后，小程序跳转到新的 URL，携带扫码结果
2. webview 检测 URL 变化，获取扫码结果

```javascript
// 小程序端
handleScanRequest(requestData) {
  wx.scanCode({
    success: (res) => {
      // 跳转到带有扫码结果的 URL
      const newUrl = `${this.data.webviewUrl}?scanResult=${encodeURIComponent(res.result)}&timestamp=${Date.now()}`
      this.setData({ webviewUrl: newUrl })
    }
  })
}
```

```javascript
// 网页端
function checkUrlForScanResult() {
  const urlParams = new URLSearchParams(window.location.search)
  const scanResult = urlParams.get('scanResult')
  
  if (scanResult) {
    // 处理扫码结果
    handleScanSuccess(decodeURIComponent(scanResult))
    
    // 清除 URL 参数
    const newUrl = window.location.pathname
    window.history.replaceState({}, '', newUrl)
  }
}

// 监听页面加载和 URL 变化
window.addEventListener('load', checkUrlForScanResult)
window.addEventListener('popstate', checkUrlForScanResult)
```

### 方案2：本地存储

1. 扫码成功后，小程序将结果存储到本地
2. webview 定期检查本地存储

### 方案3：服务器中转

1. 扫码成功后，小程序将结果发送到服务器
2. webview 通过轮询或 WebSocket 获取结果

## 测试步骤

1. 在小程序中配置 webview 页面
2. 设置正确的 webview URL
3. 在网页中点击扫码按钮
4. 检查小程序是否收到扫码请求
5. 验证扫码结果是否正确返回

## 调试技巧

1. 在小程序开发者工具中查看 console 日志
2. 使用 `wx.showModal` 显示调试信息
3. 检查 webview 的 `bindmessage` 事件是否正确触发
4. 验证消息格式是否符合协议要求

## 常见问题

### Q: webview 无法发送消息到小程序？
A: 检查是否正确引入了微信 JSSDK，确保在小程序环境中调用 `wx.miniProgram.postMessage`

### Q: 小程序收不到 webview 消息？
A: 确保 webview 组件绑定了 `bindmessage` 事件，并且消息格式正确

### Q: 扫码结果无法返回给 webview？
A: 小程序无法直接向 webview 发送消息，需要使用 URL 参数、本地存储等方式

## 完整示例

参考项目中的以下文件：
- `docs/uniapp-webview-example.vue` - uni-app 完整示例
- `src/utils/miniProgramScan.ts` - 网页端实现
- `src/pages/test-scan.vue` - 测试页面
