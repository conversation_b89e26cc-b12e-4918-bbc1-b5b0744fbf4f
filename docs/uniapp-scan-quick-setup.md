# uniapp 小程序 webview 扫码快速配置

## 🚀 快速开始

### 1. 复制 webview 页面代码

将 `docs/uniapp-webview-example.vue` 文件复制到您的 uniapp 项目中，例如：
```
pages/webview/index.vue
```

### 2. 配置页面路由

在 `pages.json` 中添加页面配置：

```json
{
  "pages": [
    {
      "path": "pages/webview/index",
      "style": {
        "navigationBarTitleText": "应用",
        "enablePullDownRefresh": false
      }
    }
  ]
}
```

### 3. 配置权限

在 `manifest.json` 中添加相机权限：

```json
{
  "mp-weixin": {
    "permission": {
      "scope.camera": {
        "desc": "用于扫码功能"
      }
    }
  }
}
```

### 4. 修改 webview URL

在 `pages/webview/index.vue` 中修改 webviewUrl：

```javascript
data() {
  return {
    webviewUrl: 'https://your-domain.com/your-page', // 改为您的实际 URL
  }
}
```

## 📱 使用方式

### 方式1：直接跳转
```javascript
uni.navigateTo({
  url: '/pages/webview/index'
})
```

### 方式2：带参数跳转
```javascript
uni.navigateTo({
  url: '/pages/webview/index?url=' + encodeURIComponent('https://your-domain.com/page') + '&title=' + encodeURIComponent('页面标题')
})
```

## 🔧 核心功能

### 扫码处理
当 webview 发送扫码请求时，小程序会：
1. 调用 `uni.scanCode()` 打开扫码界面
2. 将扫码结果通过 `postMessage` 发送回 webview
3. 显示相应的成功或失败提示

### 消息通信
支持的消息类型：
- `requestScan`: 扫码请求
- `test`: 测试消息
- `navigate`: 导航请求

### 错误处理
- 自动处理扫码失败情况
- 用户取消扫码不显示错误提示
- 权限不足时显示友好提示

## 🧪 测试验证

### 1. 检查环境
在 webview 中打开浏览器控制台，查看：
```javascript
console.log('User Agent:', navigator.userAgent)
console.log('__wxjs_environment:', window.__wxjs_environment)
```

### 2. 测试通信
在 webview 中执行：
```javascript
// 测试消息发送
if (window.wx && window.wx.miniProgram) {
  window.wx.miniProgram.postMessage({
    data: { type: 'test', message: 'Hello from webview' }
  })
}
```

### 3. 测试扫码
点击 webview 中的扫码按钮，应该能够：
- 正确打开小程序扫码界面
- 扫码成功后返回结果到 webview
- 用户取消时正确处理

## 🐛 常见问题

### Q: 扫码按钮点击无反应
**A**: 检查小程序是否正确监听了 webview 消息，查看控制台是否有相关日志。

### Q: 扫码成功但 webview 没有收到结果
**A**: 检查 `postMessage` 是否正确发送，确认 webview 组件的 ref 引用正确。

### Q: 提示没有相机权限
**A**: 确认 `manifest.json` 中已配置相机权限，并且用户已授权。

### Q: webview 加载失败
**A**: 检查 URL 是否正确，确认域名已在小程序后台配置为业务域名。

## 📋 检查清单

部署前请确认：

- [ ] 已复制 webview 页面代码
- [ ] 已配置页面路由
- [ ] 已添加相机权限
- [ ] 已修改 webviewUrl 为实际地址
- [ ] 已在小程序后台配置业务域名
- [ ] 已测试扫码功能正常
- [ ] 已测试消息通信正常

## 🔗 相关文档

- [完整配置指南](./miniprogram-webview-scan-setup.md)
- [webview 示例代码](./uniapp-webview-example.vue)
- [故障排除指南](./wechat-scan-troubleshooting.md)

## 💡 提示

1. **开发调试**: 在 webview 页面中点击"调试"按钮可以查看详细的环境信息
2. **日志查看**: 在小程序开发者工具中查看 console 日志，了解消息通信情况
3. **真机测试**: 某些功能需要在真机上测试，开发者工具可能有限制
4. **域名配置**: 确保 webview 的域名已在小程序后台配置为业务域名
