# uni-app 小程序内嵌网页扫码功能实现指南

本指南介绍如何在uni-app开发的小程序中实现内嵌网页扫码功能。

## 功能特点

- ✅ **uni-app框架**：使用uni-app开发，支持多端发布
- ✅ **无需公众号**：只需要小程序账号即可
- ✅ **自动环境检测**：网页端自动识别小程序环境
- ✅ **统一API接口**：网页端使用相同的扫码API
- ✅ **多功能支持**：扫码、定位、选择图片、用户信息等

## 项目结构

```
uniapp-miniprogram/
├── pages/
│   ├── index/
│   │   └── index.vue          # 首页
│   └── webview/
│       └── index.vue          # web-view页面
├── static/                    # 静态资源
├── App.vue                    # 应用入口
├── main.js                    # 入口文件
├── manifest.json              # 应用配置
├── pages.json                 # 页面配置
└── uni.scss                   # 全局样式
```

## 快速开始

### 1. 创建uni-app项目

```bash
# 使用HBuilderX创建项目，或使用CLI
npx @dcloudio/uniapp-cli create my-scan-app
```

### 2. 配置pages.json

```json
{
  "pages": [
    {
      "path": "pages/webview/index",
      "style": {
        "navigationBarTitleText": "网页应用",
        "enablePullDownRefresh": false
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "扫码应用",
    "navigationBarBackgroundColor": "#F8F8F8"
  }
}
```

### 3. 配置manifest.json

在`manifest.json`中配置小程序权限：

```json
{
  "mp-weixin": {
    "appid": "your-miniprogram-appid",
    "permission": {
      "scope.camera": {
        "desc": "你的摄像头将用于扫描二维码和条形码"
      },
      "scope.userLocation": {
        "desc": "你的位置信息将用于定位服务"
      }
    },
    "requiredPrivateInfos": [
      "getLocation",
      "chooseLocation"
    ]
  }
}
```

### 4. 创建web-view页面

创建 `pages/webview/index.vue`：

```vue
<template>
  <view class="webview-container">
    <web-view 
      :src="webviewUrl" 
      @message="onMessage"
      @error="onWebviewError"
      @load="onWebviewLoad"
    ></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      webviewUrl: 'https://your-domain.com/your-page'
    }
  },

  onLoad(options) {
    if (options.url) {
      this.webviewUrl = decodeURIComponent(options.url)
    }
    this.clearWebviewData()
  },

  methods: {
    // 处理网页消息
    onMessage(e) {
      const messages = e.detail.data
      if (!Array.isArray(messages) || messages.length === 0) return
      
      const latestMessage = messages[messages.length - 1]
      this.handleWebviewMessage(latestMessage)
    },

    // 处理具体消息
    handleWebviewMessage(message) {
      switch (message.type) {
        case 'requestScan':
          this.handleScanRequest(message.config || {})
          break
        case 'requestLocation':
          this.handleLocationRequest(message.config || {})
          break
        // 其他消息类型...
      }
    },

    // 处理扫码请求
    handleScanRequest(config = {}) {
      uni.scanCode({
        onlyFromCamera: config.onlyFromCamera !== false,
        scanType: config.scanType || ['qrCode', 'barCode'],
        success: (res) => {
          this.postMessageToWebview({
            type: 'scanResult',
            success: true,
            result: res.result,
            scanType: res.scanType,
            timestamp: Date.now()
          })
        },
        fail: (error) => {
          this.postMessageToWebview({
            type: 'scanResult',
            success: false,
            error: error.errMsg || '扫码失败',
            timestamp: Date.now()
          })
        }
      })
    },

    // 向网页发送消息
    postMessageToWebview(data) {
      try {
        uni.setStorageSync('webview_scan_result', JSON.stringify(data))
      } catch (error) {
        console.error('发送消息失败:', error)
      }
    },

    // 清理数据
    clearWebviewData() {
      try {
        uni.removeStorageSync('webview_scan_result')
      } catch (error) {
        console.error('清理数据失败:', error)
      }
    }
  }
}
</script>
```

## 支持的功能

### 1. 扫码功能

```javascript
// 网页端调用
const result = await wechatSDK.scanQRCode({
  scanType: ['qrCode', 'barCode']
})

// uni-app端处理
uni.scanCode({
  onlyFromCamera: true,
  scanType: ['qrCode', 'barCode'],
  success: (res) => {
    // 处理扫码结果
  }
})
```

### 2. 获取位置

```javascript
// 网页端请求位置
wx.miniProgram.postMessage({
  data: {
    type: 'requestLocation',
    config: {
      type: 'wgs84',
      altitude: true
    }
  }
})

// uni-app端处理
uni.getLocation({
  type: 'wgs84',
  altitude: true,
  success: (res) => {
    // 返回位置信息
  }
})
```

### 3. 选择图片

```javascript
// 网页端请求选择图片
wx.miniProgram.postMessage({
  data: {
    type: 'requestChooseImage',
    config: {
      count: 1,
      sourceType: ['album', 'camera']
    }
  }
})

// uni-app端处理
uni.chooseImage({
  count: 1,
  sourceType: ['album', 'camera'],
  success: (res) => {
    // 返回图片信息
  }
})
```

### 4. 获取用户信息

```javascript
// uni-app端处理用户信息请求
uni.getUserProfile({
  desc: '用于完善用户资料',
  success: (res) => {
    // 返回用户信息
  }
})
```

## 网页端配置

您的网页端已经包含了自动环境检测功能，无需额外配置。QrScanner组件会自动：

1. 检测是否在小程序环境中
2. 使用相应的扫码方式
3. 处理扫码结果

## 部署配置

### 1. 小程序管理后台配置

1. 登录微信小程序管理后台
2. 进入"开发" -> "开发管理" -> "开发设置"
3. 在"业务域名"中添加您的网页域名
4. 下载校验文件并放置在网站根目录

### 2. 域名要求

- 必须使用HTTPS协议
- 域名必须备案
- 域名必须在业务域名白名单中

### 3. 权限配置

确保在`manifest.json`中配置了必要的权限：

```json
{
  "mp-weixin": {
    "permission": {
      "scope.camera": {
        "desc": "用于扫描二维码和条形码"
      },
      "scope.userLocation": {
        "desc": "用于获取位置信息"
      },
      "scope.album": {
        "desc": "用于选择图片"
      }
    }
  }
}
```

## 调试技巧

### 1. 开发者工具调试

- 在微信开发者工具中打开小程序
- 在web-view页面中可以看到网页内容
- 使用`console.log`查看消息传递

### 2. 真机调试

- 使用微信开发者工具的真机调试功能
- 在手机上测试扫码功能
- 检查权限是否正确获取

### 3. 错误排查

常见问题及解决方案：

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 网页无法加载 | 域名未配置 | 在小程序后台配置业务域名 |
| 扫码无反应 | 权限未获取 | 检查摄像头权限配置 |
| 消息传递失败 | 存储异常 | 检查本地存储读写权限 |

## 最佳实践

1. **错误处理**：为所有API调用添加错误处理
2. **权限检查**：在调用功能前检查相应权限
3. **数据清理**：及时清理临时数据避免内存泄漏
4. **用户体验**：添加加载状态和错误提示
5. **安全考虑**：验证网页消息的合法性

## 更新日志

- **v1.0.0**: 初始版本，支持基本扫码功能
- **v1.1.0**: 添加位置、图片选择等功能
- **v1.2.0**: 完善错误处理和用户体验
