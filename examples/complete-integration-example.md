# 完整集成示例：小程序内嵌网页扫码功能

本示例展示如何完整地集成小程序内嵌网页扫码功能，包括网页端和uni-app小程序端的完整代码。

## 项目架构

```
项目结构/
├── web-app/                    # 网页应用（您当前的Vue项目）
│   ├── src/
│   │   ├── components/
│   │   │   └── QrScanner/      # 扫码组件
│   │   ├── utils/
│   │   │   └── wechat.ts       # 微信SDK工具
│   │   └── pages/
│   │       └── scan-demo.vue   # 扫码演示页面
│   └── dist/                   # 构建输出
└── miniprogram-uniapp/         # uni-app小程序
    ├── pages/
    │   └── webview/
    │       └── index.vue       # web-view页面
    ├── manifest.json           # 应用配置
    └── pages.json              # 页面配置
```

## 实现步骤

### 第一步：网页端配置

#### 1. 使用QrScanner组件

```vue
<!-- pages/scan-demo.vue -->
<template>
  <div class="scan-demo">
    <h1>扫码功能演示</h1>
    
    <!-- 环境状态显示 -->
    <div class="status-panel">
      <div class="status-item">
        <span>微信环境:</span>
        <span :class="isInWechat ? 'success' : 'error'">
          {{ isInWechat ? '✓' : '✗' }}
        </span>
      </div>
      <div class="status-item">
        <span>小程序环境:</span>
        <span :class="isMiniProgram ? 'success' : 'error'">
          {{ isMiniProgram === null ? '检测中...' : (isMiniProgram ? '✓' : '✗') }}
        </span>
      </div>
      <div class="status-item">
        <span>扫码支持:</span>
        <span :class="canScan ? 'success' : 'error'">
          {{ canScan ? '✓' : '✗' }}
        </span>
      </div>
    </div>

    <!-- 扫码按钮 -->
    <div class="scan-buttons">
      <QrScanner
        button-text="扫一扫"
        button-type="primary"
        button-size="large"
        :on-success="handleScanSuccess"
        :on-error="handleScanError"
      />
      
      <QrScanner
        button-text="仅二维码"
        button-type="success"
        :on-success="handleQrSuccess"
        :on-error="handleScanError"
      />
    </div>

    <!-- 扫码结果 -->
    <div v-if="scanResult" class="result-panel">
      <h3>扫码结果</h3>
      <div class="result-content">
        <p><strong>内容:</strong> {{ scanResult.content }}</p>
        <p><strong>类型:</strong> {{ scanResult.type }}</p>
        <p><strong>时间:</strong> {{ scanResult.time }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import QrScanner from '@/components/QrScanner/index.vue'
import { isWechatBrowser, wechatSDK } from '@/utils/wechat'

const isMiniProgram = ref<boolean | null>(null)
const canScan = ref(false)
const scanResult = ref<any>(null)

const isInWechat = computed(() => isWechatBrowser())

// 检测环境
async function detectEnvironment() {
  try {
    isMiniProgram.value = await wechatSDK.isMiniProgram()
    canScan.value = await wechatSDK.canScan()
  } catch (error) {
    console.error('环境检测失败:', error)
  }
}

// 扫码成功处理
function handleScanSuccess(result: string, parsedResult?: any) {
  scanResult.value = {
    content: result,
    type: parsedResult?.type || 'unknown',
    time: new Date().toLocaleString()
  }
}

// 二维码扫码成功
function handleQrSuccess(result: string, parsedResult?: any) {
  handleScanSuccess(result, { ...parsedResult, scanType: 'qrCode' })
}

// 扫码失败处理
function handleScanError(error: Error) {
  console.error('扫码失败:', error)
  // 可以显示错误提示
}

onMounted(() => {
  detectEnvironment()
})
</script>

<style scoped>
.scan-demo {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.status-panel {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.success { color: #52c41a; }
.error { color: #ff4d4f; }

.scan-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.result-panel {
  background: #e6f7ff;
  padding: 15px;
  border-radius: 8px;
}
</style>
```

#### 2. 路由配置

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/scan-demo',
    name: 'ScanDemo',
    component: () => import('@/pages/scan-demo.vue')
  }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

### 第二步：uni-app小程序配置

#### 1. 页面配置 (pages.json)

```json
{
  "pages": [
    {
      "path": "pages/webview/index",
      "style": {
        "navigationBarTitleText": "扫码应用",
        "enablePullDownRefresh": false
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "扫码应用",
    "navigationBarBackgroundColor": "#F8F8F8"
  }
}
```

#### 2. 应用配置 (manifest.json)

```json
{
  "mp-weixin": {
    "appid": "your-miniprogram-appid",
    "permission": {
      "scope.camera": {
        "desc": "用于扫描二维码和条形码"
      }
    },
    "requiredPrivateInfos": ["getLocation"]
  }
}
```

#### 3. web-view页面 (pages/webview/index.vue)

```vue
<template>
  <view class="webview-container">
    <web-view 
      :src="webviewUrl" 
      @message="onMessage"
      @error="onWebviewError"
      @load="onWebviewLoad"
    ></web-view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      webviewUrl: 'https://your-domain.com/scan-demo',
      loading: true
    }
  },

  onLoad(options) {
    // 支持通过参数传递URL
    if (options.url) {
      this.webviewUrl = decodeURIComponent(options.url)
    }
    
    // 清理之前的数据
    this.clearWebviewData()
  },

  methods: {
    // 处理网页消息
    onMessage(e) {
      const messages = e.detail.data
      if (!Array.isArray(messages) || messages.length === 0) return
      
      const latestMessage = messages[messages.length - 1]
      this.handleWebviewMessage(latestMessage)
    },

    // 处理具体消息
    handleWebviewMessage(message) {
      console.log('收到消息:', message)
      
      switch (message.type) {
        case 'requestScan':
          this.handleScanRequest(message.config || {})
          break
        default:
          console.log('未知消息类型:', message.type)
      }
    },

    // 处理扫码请求
    handleScanRequest(config = {}) {
      console.log('开始扫码:', config)
      
      uni.scanCode({
        onlyFromCamera: config.onlyFromCamera !== false,
        scanType: config.scanType || ['qrCode', 'barCode'],
        success: (res) => {
          console.log('扫码成功:', res)
          
          // 将结果存储到本地，供网页读取
          this.postMessageToWebview({
            type: 'scanResult',
            success: true,
            result: res.result,
            scanType: res.scanType,
            timestamp: Date.now()
          })
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          
          this.postMessageToWebview({
            type: 'scanResult',
            success: false,
            error: error.errMsg || '扫码失败',
            timestamp: Date.now()
          })
        }
      })
    },

    // 向网页发送消息
    postMessageToWebview(data) {
      try {
        uni.setStorageSync('webview_scan_result', JSON.stringify(data))
        console.log('消息已发送:', data)
      } catch (error) {
        console.error('发送消息失败:', error)
      }
    },

    // 清理数据
    clearWebviewData() {
      try {
        uni.removeStorageSync('webview_scan_result')
      } catch (error) {
        console.error('清理数据失败:', error)
      }
    },

    // 网页加载完成
    onWebviewLoad() {
      this.loading = false
      console.log('网页加载完成')
    },

    // 网页加载错误
    onWebviewError(e) {
      this.loading = false
      console.error('网页加载失败:', e)
      
      uni.showToast({
        title: '网页加载失败',
        icon: 'none'
      })
    }
  }
}
</script>

<style>
.webview-container {
  width: 100%;
  height: 100vh;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
}
</style>
```

### 第三步：部署配置

#### 1. 网页端部署

```bash
# 构建生产版本
npm run build

# 部署到HTTPS服务器
# 确保域名已备案且支持HTTPS
```

#### 2. 小程序端配置

1. **业务域名配置**
   - 登录微信小程序管理后台
   - 进入"开发设置" -> "业务域名"
   - 添加您的网页域名：`https://your-domain.com`
   - 下载校验文件并上传到网站根目录

2. **权限配置**
   - 确保已配置摄像头权限
   - 如需其他功能，配置相应权限

3. **发布小程序**
   ```bash
   # 使用HBuilderX或CLI工具发布
   # 上传代码到微信开发者工具
   # 提交审核并发布
   ```

## 测试流程

### 1. 开发环境测试

```bash
# 启动网页开发服务器
npm run dev

# 在微信开发者工具中打开小程序
# 将webviewUrl设置为本地开发地址（需要内网穿透）
```

### 2. 生产环境测试

1. 部署网页到HTTPS服务器
2. 配置小程序业务域名
3. 在真机上测试扫码功能

### 3. 功能验证清单

- [ ] 网页在小程序中正常加载
- [ ] 环境检测正确识别小程序环境
- [ ] 扫码按钮可以正常点击
- [ ] 扫码功能正常工作
- [ ] 扫码结果正确返回网页
- [ ] 错误情况正确处理

## 常见问题解决

### 1. 网页无法加载
- 检查业务域名配置
- 确认网站支持HTTPS
- 验证域名备案状态

### 2. 扫码无反应
- 检查摄像头权限
- 查看控制台错误信息
- 确认消息传递机制

### 3. 结果无法返回
- 检查本地存储权限
- 验证轮询机制
- 查看数据格式

通过以上完整的集成示例，您可以成功实现小程序内嵌网页的扫码功能。
