<template>
  <view class="webview-container">
    <!-- web-view组件 -->
    <web-view 
      :src="webviewUrl" 
      @message="onMessage"
      @error="onWebviewError"
      @load="onWebviewLoad"
    ></web-view>
    
    <!-- 加载提示 -->
    <view v-if="loading" class="loading-mask">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
    
    <!-- 错误提示 -->
    <view v-if="error" class="error-mask">
      <view class="error-content">
        <text class="error-icon">⚠️</text>
        <text class="error-text">{{ errorMessage }}</text>
        <button class="retry-button" @tap="retryLoad">重新加载</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 网页URL - 请替换为您的实际网页地址
      webviewUrl: 'https://your-domain.com/your-page',
      
      // 页面状态
      loading: true,
      error: false,
      errorMessage: ''
    }
  },

  onLoad(options) {
    console.log('webview页面加载', options)
    
    // 如果通过参数传递了URL，使用参数中的URL
    if (options.url) {
      this.webviewUrl = decodeURIComponent(options.url)
    }
    
    // 清理之前可能残留的数据
    this.clearWebviewData()
  },

  onShow() {
    console.log('webview页面显示')
  },

  onHide() {
    console.log('webview页面隐藏')
  },

  onUnload() {
    console.log('webview页面卸载')
    // 清理数据
    this.clearWebviewData()
  },

  methods: {
    /**
     * 处理网页发送的消息
     */
    onMessage(e) {
      console.log('收到网页消息:', e.detail.data)
      
      const messages = e.detail.data
      if (!Array.isArray(messages) || messages.length === 0) {
        return
      }

      // 处理最新的消息
      const latestMessage = messages[messages.length - 1]
      this.handleWebviewMessage(latestMessage)
    },

    /**
     * 网页加载完成
     */
    onWebviewLoad(e) {
      console.log('网页加载完成:', e)
      this.loading = false
      this.error = false
    },

    /**
     * 网页加载错误
     */
    onWebviewError(e) {
      console.error('网页加载错误:', e)
      this.loading = false
      this.error = true
      this.errorMessage = '网页加载失败，请检查网络连接'
    },

    /**
     * 重新加载网页
     */
    retryLoad() {
      this.loading = true
      this.error = false
      this.errorMessage = ''
      
      // 强制刷新webview
      const currentUrl = this.webviewUrl
      this.webviewUrl = ''
      
      this.$nextTick(() => {
        this.webviewUrl = currentUrl
      })
    },

    /**
     * 处理具体的网页消息
     */
    handleWebviewMessage(message) {
      if (!message || !message.type) {
        console.warn('无效的消息格式:', message)
        return
      }

      console.log('处理消息类型:', message.type)

      switch (message.type) {
        case 'requestScan':
          this.handleScanRequest(message.config || {})
          break
        case 'requestLocation':
          this.handleLocationRequest(message.config || {})
          break
        case 'requestUserInfo':
          this.handleUserInfoRequest(message.config || {})
          break
        case 'requestChooseImage':
          this.handleChooseImageRequest(message.config || {})
          break
        case 'requestShare':
          this.handleShareRequest(message.config || {})
          break
        default:
          console.log('未知消息类型:', message.type)
          this.postMessageToWebview({
            type: message.type + 'Result',
            success: false,
            error: '不支持的操作类型'
          })
      }
    },

    /**
     * 处理扫码请求
     */
    handleScanRequest(config = {}) {
      console.log('处理扫码请求:', config)

      uni.scanCode({
        onlyFromCamera: config.onlyFromCamera !== false,
        scanType: config.scanType || ['qrCode', 'barCode'],
        autoDecodeCharSet: true,
        success: (res) => {
          console.log('扫码成功:', res)
          
          this.postMessageToWebview({
            type: 'scanResult',
            success: true,
            result: res.result,
            scanType: res.scanType,
            charSet: res.charSet,
            path: res.path,
            timestamp: Date.now()
          })
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          
          let errorMessage = '扫码失败'
          if (error.errMsg) {
            if (error.errMsg.includes('cancel')) {
              errorMessage = '用户取消'
            } else if (error.errMsg.includes('fail')) {
              errorMessage = '扫码失败'
            }
          }
          
          this.postMessageToWebview({
            type: 'scanResult',
            success: false,
            error: errorMessage,
            timestamp: Date.now()
          })
        }
      })
    },

    /**
     * 处理位置请求
     */
    handleLocationRequest(config = {}) {
      console.log('处理位置请求:', config)

      uni.getLocation({
        type: config.type || 'wgs84',
        altitude: config.altitude || false,
        geocode: config.geocode || false,
        highAccuracyExpireTime: config.highAccuracyExpireTime || 4000,
        isHighAccuracy: config.isHighAccuracy || false,
        success: (res) => {
          console.log('获取位置成功:', res)
          
          this.postMessageToWebview({
            type: 'locationResult',
            success: true,
            latitude: res.latitude,
            longitude: res.longitude,
            speed: res.speed,
            accuracy: res.accuracy,
            altitude: res.altitude,
            verticalAccuracy: res.verticalAccuracy,
            horizontalAccuracy: res.horizontalAccuracy,
            address: res.address,
            timestamp: Date.now()
          })
        },
        fail: (error) => {
          console.error('获取位置失败:', error)
          
          this.postMessageToWebview({
            type: 'locationResult',
            success: false,
            error: error.errMsg || '获取位置失败',
            timestamp: Date.now()
          })
        }
      })
    },

    /**
     * 处理用户信息请求
     */
    handleUserInfoRequest(config = {}) {
      console.log('处理用户信息请求:', config)

      // uni-app中获取用户信息
      uni.getUserProfile({
        desc: config.desc || '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功:', res)
          
          this.postMessageToWebview({
            type: 'userInfoResult',
            success: true,
            userInfo: res.userInfo,
            rawData: res.rawData,
            signature: res.signature,
            encryptedData: res.encryptedData,
            iv: res.iv,
            timestamp: Date.now()
          })
        },
        fail: (error) => {
          console.error('获取用户信息失败:', error)
          
          this.postMessageToWebview({
            type: 'userInfoResult',
            success: false,
            error: error.errMsg || '获取用户信息失败',
            timestamp: Date.now()
          })
        }
      })
    },

    /**
     * 处理选择图片请求
     */
    handleChooseImageRequest(config = {}) {
      console.log('处理选择图片请求:', config)

      uni.chooseImage({
        count: config.count || 1,
        sizeType: config.sizeType || ['original', 'compressed'],
        sourceType: config.sourceType || ['album', 'camera'],
        success: (res) => {
          console.log('选择图片成功:', res)
          
          this.postMessageToWebview({
            type: 'chooseImageResult',
            success: true,
            tempFilePaths: res.tempFilePaths,
            tempFiles: res.tempFiles,
            timestamp: Date.now()
          })
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          
          this.postMessageToWebview({
            type: 'chooseImageResult',
            success: false,
            error: error.errMsg || '选择图片失败',
            timestamp: Date.now()
          })
        }
      })
    },

    /**
     * 处理分享请求
     */
    handleShareRequest(config = {}) {
      console.log('处理分享请求:', config)
      
      try {
        uni.setStorageSync('share_config', config)
        
        this.postMessageToWebview({
          type: 'shareResult',
          success: true,
          message: '分享配置已设置',
          timestamp: Date.now()
        })
      } catch (error) {
        this.postMessageToWebview({
          type: 'shareResult',
          success: false,
          error: '设置分享配置失败',
          timestamp: Date.now()
        })
      }
    },

    /**
     * 向网页发送消息
     */
    postMessageToWebview(data) {
      console.log('向网页发送消息:', data)
      
      try {
        // 使用uni-app的本地存储传递数据给网页
        uni.setStorageSync('webview_scan_result', JSON.stringify(data))
        
      } catch (error) {
        console.error('发送消息到网页失败:', error)
      }
    },

    /**
     * 清理网页相关数据
     */
    clearWebviewData() {
      try {
        uni.removeStorageSync('webview_scan_result')
        uni.removeStorageSync('share_config')
      } catch (error) {
        console.error('清理数据失败:', error)
      }
    }
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    try {
      const shareConfig = uni.getStorageSync('share_config') || {}
      
      return {
        title: shareConfig.title || '扫码应用',
        path: shareConfig.path || '/pages/webview/index',
        imageUrl: shareConfig.imageUrl || ''
      }
    } catch (error) {
      console.error('获取分享配置失败:', error)
      return {
        title: '扫码应用',
        path: '/pages/webview/index'
      }
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    try {
      const shareConfig = uni.getStorageSync('share_config') || {}
      
      return {
        title: shareConfig.title || '扫码应用',
        query: shareConfig.query || '',
        imageUrl: shareConfig.imageUrl || ''
      }
    } catch (error) {
      console.error('获取分享配置失败:', error)
      return {
        title: '扫码应用'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.webview-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1aad19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误遮罩 */
.error-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-button {
  background-color: #1aad19;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.retry-button:active {
  background-color: #179b16;
}
</style>
