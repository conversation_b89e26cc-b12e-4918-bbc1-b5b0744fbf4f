<template>
  <div class="miniprogram-demo">
    <div class="header">
      <h1>小程序内嵌网页扫码演示</h1>
      <p class="subtitle">支持自动环境检测，无需公众号配置</p>
    </div>

    <div class="status-section">
      <div class="status-card">
        <h3>环境检测</h3>
        <div class="status-item">
          <span class="label">微信环境:</span>
          <span :class="['status', isInWechat ? 'success' : 'error']">
            {{ isInWechat ? '✓ 是' : '✗ 否' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">小程序环境:</span>
          <span :class="['status', isMiniProgram ? 'success' : 'error']">
            {{ isMiniProgram === null ? '检测中...' : (isMiniProgram ? '✓ 是' : '✗ 否') }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">扫码支持:</span>
          <span :class="['status', canScan ? 'success' : 'error']">
            {{ canScan ? '✓ 支持' : '✗ 不支持' }}
          </span>
        </div>
      </div>
    </div>

    <div class="scan-section">
      <div class="scan-card">
        <h3>扫码功能</h3>
        <div class="scan-buttons">
          <QrScanner
            button-text="扫一扫"
            button-type="primary"
            button-size="large"
            :on-success="handleScanSuccess"
            :on-error="handleScanError"
          />
          
          <QrScanner
            button-text="仅二维码"
            button-type="success"
            :on-success="handleQrSuccess"
            :on-error="handleScanError"
          />
          
          <QrScanner
            button-text="仅条形码"
            button-type="warning"
            :on-success="handleBarSuccess"
            :on-error="handleScanError"
          />
        </div>
        
        <div v-if="!canScan" class="scan-tips">
          <p class="tip-title">扫码功能不可用</p>
          <ul class="tip-list">
            <li v-if="!isInWechat">请在微信中打开此页面</li>
            <li v-else-if="isMiniProgram === false">
              请在小程序中使用web-view组件加载此页面，或配置公众号JSSDK
            </li>
            <li v-else>正在检测环境，请稍候...</li>
          </ul>
        </div>
      </div>
    </div>

    <div v-if="scanResults.length > 0" class="results-section">
      <div class="results-card">
        <h3>扫码历史</h3>
        <div class="results-list">
          <div
            v-for="(result, index) in scanResults"
            :key="index"
            class="result-item"
          >
            <div class="result-header">
              <span class="result-time">{{ formatTime(result.timestamp) }}</span>
              <span :class="['result-type', result.type]">{{ result.type }}</span>
            </div>
            <div class="result-content">
              <div class="result-text">{{ result.content }}</div>
              <div v-if="result.parsed" class="result-parsed">
                <strong>解析结果:</strong>
                <pre>{{ JSON.stringify(result.parsed, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>
        <button class="clear-button" @click="clearResults">清空历史</button>
      </div>
    </div>

    <div class="help-section">
      <div class="help-card">
        <h3>使用说明</h3>
        <div class="help-content">
          <h4>小程序配置步骤：</h4>
          <ol>
            <li>在小程序管理后台配置业务域名</li>
            <li>创建包含web-view的小程序页面</li>
            <li>添加消息处理逻辑</li>
            <li>配置摄像头权限</li>
          </ol>
          
          <h4>支持的扫码类型：</h4>
          <ul>
            <li>二维码 (QR Code)</li>
            <li>条形码 (Bar Code)</li>
            <li>Data Matrix</li>
            <li>PDF417</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import QrScanner from '@/components/QrScanner/index.vue'
import { isWechatBrowser, wechatSDK } from '@/utils/wechat'

// 响应式状态
const isMiniProgram = ref<boolean | null>(null)
const canScan = ref(false)
const scanResults = ref<Array<{
  timestamp: number
  type: string
  content: string
  parsed?: any
}>>([])

// 计算属性
const isInWechat = computed(() => isWechatBrowser())

// 检测环境
async function detectEnvironment() {
  try {
    // 检测是否在小程序环境中
    isMiniProgram.value = await wechatSDK.isMiniProgram()
    
    // 检测是否支持扫码
    canScan.value = await wechatSDK.canScan()
    
    console.log('环境检测结果:', {
      isInWechat: isInWechat.value,
      isMiniProgram: isMiniProgram.value,
      canScan: canScan.value
    })
  } catch (error) {
    console.error('环境检测失败:', error)
    isMiniProgram.value = false
    canScan.value = false
  }
}

// 扫码成功处理
function handleScanSuccess(result: string, parsedResult?: any) {
  console.log('扫码成功:', result, parsedResult)
  
  scanResults.value.unshift({
    timestamp: Date.now(),
    type: parsedResult?.type || 'unknown',
    content: result,
    parsed: parsedResult
  })
  
  // 保留最近20条记录
  if (scanResults.value.length > 20) {
    scanResults.value = scanResults.value.slice(0, 20)
  }
}

// 二维码扫码成功
function handleQrSuccess(result: string, parsedResult?: any) {
  console.log('二维码扫码成功:', result)
  handleScanSuccess(result, { ...parsedResult, scanType: 'qrCode' })
}

// 条形码扫码成功
function handleBarSuccess(result: string, parsedResult?: any) {
  console.log('条形码扫码成功:', result)
  handleScanSuccess(result, { ...parsedResult, scanType: 'barCode' })
}

// 扫码失败处理
function handleScanError(error: Error) {
  console.error('扫码失败:', error)
  
  // 可以在这里显示错误提示
  // showToast(error.message)
}

// 格式化时间
function formatTime(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 清空扫码历史
function clearResults() {
  scanResults.value = []
}

// 组件挂载时检测环境
onMounted(() => {
  detectEnvironment()
})
</script>

<style lang="less" scoped>
.miniprogram-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: #333;
    margin-bottom: 10px;
  }
  
  .subtitle {
    color: #666;
    font-size: 14px;
  }
}

.status-section,
.scan-section,
.results-section,
.help-section {
  margin-bottom: 30px;
}

.status-card,
.scan-card,
.results-card,
.help-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
  }
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    color: #666;
  }
  
  .status {
    font-weight: 500;
    
    &.success {
      color: #52c41a;
    }
    
    &.error {
      color: #ff4d4f;
    }
  }
}

.scan-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.scan-tips {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 15px;
  
  .tip-title {
    color: #d46b08;
    font-weight: 500;
    margin: 0 0 10px 0;
  }
  
  .tip-list {
    margin: 0;
    padding-left: 20px;
    color: #d46b08;
    
    li {
      margin-bottom: 5px;
    }
  }
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
  
  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .result-time {
      color: #999;
      font-size: 12px;
    }
    
    .result-type {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      
      &.workstation_info {
        background: #e6f7ff;
        color: #1890ff;
      }
      
      &.login {
        background: #f6ffed;
        color: #52c41a;
      }
      
      &.url {
        background: #fff7e6;
        color: #fa8c16;
      }
      
      &.text {
        background: #f0f0f0;
        color: #666;
      }
    }
  }
  
  .result-content {
    .result-text {
      word-break: break-all;
      margin-bottom: 10px;
      font-family: monospace;
      background: #f8f8f8;
      padding: 8px;
      border-radius: 4px;
    }
    
    .result-parsed {
      pre {
        background: #f0f0f0;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
        margin: 5px 0 0 0;
      }
    }
  }
}

.clear-button {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background: #ff7875;
  }
}

.help-content {
  h4 {
    color: #333;
    margin: 15px 0 10px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  ol, ul {
    margin: 0 0 15px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 5px;
      color: #666;
    }
  }
}

@media (max-width: 768px) {
  .miniprogram-demo {
    padding: 15px;
  }
  
  .scan-buttons {
    flex-direction: column;
  }
}
</style>
