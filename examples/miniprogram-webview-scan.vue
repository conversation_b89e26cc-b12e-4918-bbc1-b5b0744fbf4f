<!-- 
  小程序 webview 扫码功能完整示例
  适用于 uni-app 微信小程序
  
  使用方法：
  1. 将此文件放到小程序的 pages 目录下
  2. 在 pages.json 中注册页面
  3. 修改 webviewUrl 为你的网页地址
  4. 确保网页域名已在小程序后台配置为业务域名
-->

<template>
  <view class="webview-page">
    <!-- 顶部状态栏 -->
    <view class="status-bar" v-if="showStatus">
      <text class="status-text">{{ statusText }}</text>
    </view>
    
    <!-- webview 容器 -->
    <view class="webview-container">
      <web-view 
        ref="webview"
        :src="currentUrl" 
        @message="onWebviewMessage"
        @load="onWebviewLoad"
        @error="onWebviewError">
      </web-view>
    </view>
    
    <!-- 调试面板（开发环境显示） -->
    <view class="debug-panel" v-if="isDev && showDebug">
      <view class="debug-header" @click="toggleDebug">
        <text>调试信息</text>
        <text class="debug-toggle">{{ debugExpanded ? '收起' : '展开' }}</text>
      </view>
      
      <view class="debug-content" v-if="debugExpanded">
        <view class="debug-item">
          <text class="debug-label">当前URL:</text>
          <text class="debug-value">{{ currentUrl }}</text>
        </view>
        
        <view class="debug-item">
          <text class="debug-label">消息数量:</text>
          <text class="debug-value">{{ messageHistory.length }}</text>
        </view>
        
        <view class="debug-item">
          <text class="debug-label">最新消息:</text>
          <text class="debug-value">{{ lastMessage }}</text>
        </view>
        
        <button class="debug-btn" @click="testScan">测试扫码</button>
        <button class="debug-btn" @click="clearHistory">清除历史</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 基础配置
      baseUrl: 'https://your-domain.com/your-page', // 修改为你的网页地址
      currentUrl: '',
      
      // 状态管理
      showStatus: false,
      statusText: '',
      
      // 调试相关
      isDev: process.env.NODE_ENV === 'development',
      showDebug: true,
      debugExpanded: false,
      messageHistory: [],
      lastMessage: '无',
      
      // 扫码相关
      scanningRequestId: null,
      scanTimeout: null
    }
  },

  onLoad(options) {
    console.log('webview 页面加载', options)
    
    // 从参数获取 URL
    let targetUrl = this.baseUrl
    if (options.url) {
      targetUrl = decodeURIComponent(options.url)
    }
    
    // 添加小程序标识参数
    const separator = targetUrl.includes('?') ? '&' : '?'
    targetUrl += `${separator}from=miniprogram&timestamp=${Date.now()}`
    
    this.currentUrl = targetUrl
    this.showStatus = true
    this.statusText = '正在加载...'
    
    console.log('最终 URL:', this.currentUrl)
  },

  onShow() {
    console.log('页面显示')
  },

  onHide() {
    console.log('页面隐藏')
    this.clearScanTimeout()
  },

  onUnload() {
    console.log('页面卸载')
    this.clearScanTimeout()
  },

  methods: {
    // webview 加载完成
    onWebviewLoad(e) {
      console.log('webview 加载完成', e)
      this.statusText = '加载完成'
      
      setTimeout(() => {
        this.showStatus = false
      }, 2000)
    },

    // webview 加载错误
    onWebviewError(e) {
      console.error('webview 加载错误', e)
      this.statusText = '加载失败'
      
      uni.showModal({
        title: '页面加载失败',
        content: '请检查网络连接或联系客服',
        showCancel: false
      })
    },

    // 监听 webview 消息
    onWebviewMessage(e) {
      console.log('收到 webview 消息', e.detail.data)
      
      if (e.detail.data && e.detail.data.length > 0) {
        // 处理所有消息
        e.detail.data.forEach(data => {
          this.handleWebviewMessage(data)
        })
        
        // 更新调试信息
        this.messageHistory.push(...e.detail.data)
        this.lastMessage = JSON.stringify(e.detail.data[e.detail.data.length - 1])
      }
    },

    // 处理 webview 消息
    handleWebviewMessage(data) {
      console.log('处理消息', data)
      
      if (!data || typeof data !== 'object') {
        console.warn('无效的消息格式', data)
        return
      }
      
      switch (data.type) {
        case 'requestScan':
          this.handleScanRequest(data)
          break
        case 'test':
          this.handleTestMessage(data)
          break
        default:
          console.log('未知消息类型', data.type)
      }
    },

    // 处理扫码请求
    handleScanRequest(requestData) {
      console.log('开始扫码', requestData)
      
      // 记录请求ID
      this.scanningRequestId = requestData.requestId
      
      // 设置超时
      this.scanTimeout = setTimeout(() => {
        console.log('扫码超时')
        this.sendScanResult({
          success: false,
          error: '扫码超时',
          requestId: this.scanningRequestId
        })
        this.scanningRequestId = null
      }, 30000) // 30秒超时
      
      // 显示扫码状态
      this.statusText = '正在扫码...'
      this.showStatus = true
      
      // 调用小程序扫码 API
      uni.scanCode({
        success: (res) => {
          console.log('扫码成功', res)
          this.clearScanTimeout()
          
          this.statusText = '扫码成功'
          
          // 发送成功结果
          this.sendScanResult({
            success: true,
            result: res.result,
            requestId: this.scanningRequestId
          })
          
          // 隐藏状态
          setTimeout(() => {
            this.showStatus = false
          }, 1500)
        },
        
        fail: (error) => {
          console.error('扫码失败', error)
          this.clearScanTimeout()
          
          let errorMessage = '扫码失败'
          let isCancelled = false
          
          if (error.errMsg) {
            if (error.errMsg.includes('cancel')) {
              errorMessage = '用户取消扫码'
              isCancelled = true
            } else if (error.errMsg.includes('permission')) {
              errorMessage = '没有相机权限'
            } else {
              errorMessage = error.errMsg
            }
          }
          
          this.statusText = errorMessage
          
          if (isCancelled) {
            // 发送取消消息
            this.sendScanCancel(this.scanningRequestId)
          } else {
            // 发送失败结果
            this.sendScanResult({
              success: false,
              error: errorMessage,
              requestId: this.scanningRequestId
            })
            
            // 显示错误提示
            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 2000
            })
          }
          
          // 隐藏状态
          setTimeout(() => {
            this.showStatus = false
          }, 2000)
        }
      })
    },

    // 处理测试消息
    handleTestMessage(data) {
      console.log('收到测试消息', data.message)
      
      uni.showToast({
        title: '收到测试消息',
        icon: 'success'
      })
    },

    // 发送扫码结果（通过 URL 参数）
    sendScanResult(result) {
      console.log('发送扫码结果', result)
      
      try {
        // 构造结果参数
        const resultData = {
          type: 'scanResult',
          success: result.success,
          result: result.result || '',
          error: result.error || '',
          timestamp: Date.now(),
          requestId: result.requestId || ''
        }
        
        // 通过 URL 参数传递结果
        const resultParam = encodeURIComponent(JSON.stringify(resultData))
        const separator = this.currentUrl.includes('?') ? '&' : '?'
        const newUrl = `${this.currentUrl}${separator}scanData=${resultParam}`
        
        console.log('跳转到新 URL:', newUrl)
        this.currentUrl = newUrl
        
      } catch (error) {
        console.error('发送扫码结果失败', error)
      } finally {
        this.scanningRequestId = null
      }
    },

    // 发送取消消息
    sendScanCancel(requestId) {
      console.log('发送取消消息', requestId)
      
      const cancelData = {
        type: 'scanCancel',
        timestamp: Date.now(),
        requestId: requestId || ''
      }
      
      const resultParam = encodeURIComponent(JSON.stringify(cancelData))
      const separator = this.currentUrl.includes('?') ? '&' : '?'
      const newUrl = `${this.currentUrl}${separator}scanData=${resultParam}`
      
      this.currentUrl = newUrl
      this.scanningRequestId = null
    },

    // 清除扫码超时
    clearScanTimeout() {
      if (this.scanTimeout) {
        clearTimeout(this.scanTimeout)
        this.scanTimeout = null
      }
    },

    // 调试相关方法
    toggleDebug() {
      this.debugExpanded = !this.debugExpanded
    },

    testScan() {
      this.handleScanRequest({
        type: 'requestScan',
        timestamp: Date.now(),
        requestId: 'test-' + Math.random().toString(36).substring(2, 9)
      })
    },

    clearHistory() {
      this.messageHistory = []
      this.lastMessage = '无'
    }
  }
}
</script>

<style scoped>
.webview-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.status-text {
  color: #ffffff;
  font-size: 14px;
}

.webview-container {
  flex: 1;
  width: 100%;
}

.debug-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.9);
  color: #ffffff;
  z-index: 1001;
}

.debug-header {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.debug-toggle {
  font-size: 12px;
  color: #00d4ff;
}

.debug-content {
  padding: 10px 15px;
  max-height: 200px;
  overflow-y: auto;
}

.debug-item {
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
}

.debug-label {
  font-size: 12px;
  color: #cccccc;
  margin-bottom: 2px;
}

.debug-value {
  font-size: 11px;
  color: #ffffff;
  word-break: break-all;
}

.debug-btn {
  margin: 5px 5px 5px 0;
  padding: 5px 10px;
  background-color: #007aff;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 12px;
}
</style>
