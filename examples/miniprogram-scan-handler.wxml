<!--
  小程序页面模板：包含web-view组件用于加载网页
  
  使用方法：
  1. 将此文件保存为您的小程序页面的 .wxml 文件
  2. 确保对应的 .js 文件包含了消息处理逻辑
  3. 在小程序管理后台配置业务域名白名单
-->

<view class="container">
  <!-- 网页容器 -->
  <web-view 
    src="{{webviewUrl}}" 
    bindmessage="onMessage"
    binderror="onWebviewError"
    bindload="onWebviewLoad"
  ></web-view>
  
  <!-- 加载提示（可选） -->
  <view class="loading-mask" wx:if="{{showLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
  
  <!-- 错误提示（可选） -->
  <view class="error-mask" wx:if="{{showError}}">
    <view class="error-content">
      <text class="error-icon">⚠️</text>
      <text class="error-text">{{errorMessage}}</text>
      <button class="retry-button" bindtap="retryLoad">重新加载</button>
    </view>
  </view>
</view>
