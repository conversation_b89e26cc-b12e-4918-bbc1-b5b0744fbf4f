/**
 * 小程序web-view页面
 * 处理内嵌网页的各种请求，包括扫码、定位等
 */

Page({
  data: {
    // 网页URL - 请替换为您的实际网页地址
    webviewUrl: 'https://your-domain.com/your-page',
    
    // 页面状态
    loading: true,
    error: false,
    errorMessage: ''
  },

  onLoad(options) {
    console.log('webview页面加载', options)
    
    // 如果通过参数传递了URL，使用参数中的URL
    if (options.url) {
      this.setData({
        webviewUrl: decodeURIComponent(options.url)
      })
    }
    
    // 清理之前可能残留的数据
    this.clearWebviewData()
  },

  onShow() {
    console.log('webview页面显示')
  },

  onHide() {
    console.log('webview页面隐藏')
  },

  onUnload() {
    console.log('webview页面卸载')
    // 清理数据
    this.clearWebviewData()
  },

  /**
   * 处理网页发送的消息
   */
  onMessage(e) {
    console.log('收到网页消息:', e.detail.data)
    
    const messages = e.detail.data
    if (!Array.isArray(messages) || messages.length === 0) {
      return
    }

    // 处理最新的消息
    const latestMessage = messages[messages.length - 1]
    this.handleWebviewMessage(latestMessage)
  },

  /**
   * 网页加载完成
   */
  onWebviewLoad(e) {
    console.log('网页加载完成:', e)
    this.setData({
      loading: false,
      error: false
    })
  },

  /**
   * 网页加载错误
   */
  onWebviewError(e) {
    console.error('网页加载错误:', e)
    this.setData({
      loading: false,
      error: true,
      errorMessage: '网页加载失败，请检查网络连接'
    })
  },

  /**
   * 重新加载网页
   */
  retryLoad() {
    this.setData({
      loading: true,
      error: false,
      errorMessage: ''
    })
    
    // 强制刷新webview
    const currentUrl = this.data.webviewUrl
    this.setData({
      webviewUrl: ''
    })
    
    setTimeout(() => {
      this.setData({
        webviewUrl: currentUrl
      })
    }, 100)
  },

  /**
   * 处理具体的网页消息
   */
  handleWebviewMessage(message) {
    if (!message || !message.type) {
      console.warn('无效的消息格式:', message)
      return
    }

    console.log('处理消息类型:', message.type)

    switch (message.type) {
      case 'requestScan':
        this.handleScanRequest(message.config || {})
        break
      case 'requestLocation':
        this.handleLocationRequest(message.config || {})
        break
      case 'requestUserInfo':
        this.handleUserInfoRequest(message.config || {})
        break
      case 'requestShare':
        this.handleShareRequest(message.config || {})
        break
      case 'requestPayment':
        this.handlePaymentRequest(message.config || {})
        break
      default:
        console.log('未知消息类型:', message.type)
        this.postMessageToWebview({
          type: message.type + 'Result',
          success: false,
          error: '不支持的操作类型'
        })
    }
  },

  /**
   * 处理扫码请求
   */
  handleScanRequest(config = {}) {
    console.log('处理扫码请求:', config)

    wx.scanCode({
      onlyFromCamera: config.onlyFromCamera !== false,
      scanType: config.scanType || ['qrCode', 'barCode'],
      success: (res) => {
        console.log('扫码成功:', res)
        
        this.postMessageToWebview({
          type: 'scanResult',
          success: true,
          result: res.result,
          scanType: res.scanType,
          charSet: res.charSet,
          path: res.path,
          timestamp: Date.now()
        })
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        
        let errorMessage = '扫码失败'
        if (error.errMsg) {
          if (error.errMsg.includes('cancel')) {
            errorMessage = '用户取消'
          } else if (error.errMsg.includes('fail')) {
            errorMessage = '扫码失败'
          }
        }
        
        this.postMessageToWebview({
          type: 'scanResult',
          success: false,
          error: errorMessage,
          timestamp: Date.now()
        })
      }
    })
  },

  /**
   * 处理位置请求
   */
  handleLocationRequest(config = {}) {
    console.log('处理位置请求:', config)

    wx.getLocation({
      type: config.type || 'wgs84',
      altitude: config.altitude || false,
      isHighAccuracy: config.isHighAccuracy || false,
      highAccuracyExpireTime: config.highAccuracyExpireTime || 4000,
      success: (res) => {
        console.log('获取位置成功:', res)
        
        this.postMessageToWebview({
          type: 'locationResult',
          success: true,
          latitude: res.latitude,
          longitude: res.longitude,
          speed: res.speed,
          accuracy: res.accuracy,
          altitude: res.altitude,
          verticalAccuracy: res.verticalAccuracy,
          horizontalAccuracy: res.horizontalAccuracy,
          timestamp: Date.now()
        })
      },
      fail: (error) => {
        console.error('获取位置失败:', error)
        
        this.postMessageToWebview({
          type: 'locationResult',
          success: false,
          error: error.errMsg || '获取位置失败',
          timestamp: Date.now()
        })
      }
    })
  },

  /**
   * 处理用户信息请求
   */
  handleUserInfoRequest(config = {}) {
    console.log('处理用户信息请求:', config)

    // 注意：getUserProfile 需要用户主动触发
    // 这里只是示例，实际使用时需要在用户点击按钮时调用
    wx.getUserProfile({
      desc: config.desc || '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res)
        
        this.postMessageToWebview({
          type: 'userInfoResult',
          success: true,
          userInfo: res.userInfo,
          rawData: res.rawData,
          signature: res.signature,
          encryptedData: res.encryptedData,
          iv: res.iv,
          timestamp: Date.now()
        })
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error)
        
        this.postMessageToWebview({
          type: 'userInfoResult',
          success: false,
          error: error.errMsg || '获取用户信息失败',
          timestamp: Date.now()
        })
      }
    })
  },

  /**
   * 处理分享请求
   */
  handleShareRequest(config = {}) {
    console.log('处理分享请求:', config)
    
    // 小程序分享需要在页面的 onShareAppMessage 中处理
    // 这里只是记录分享配置
    try {
      wx.setStorageSync('share_config', config)
      
      this.postMessageToWebview({
        type: 'shareResult',
        success: true,
        message: '分享配置已设置',
        timestamp: Date.now()
      })
    } catch (error) {
      this.postMessageToWebview({
        type: 'shareResult',
        success: false,
        error: '设置分享配置失败',
        timestamp: Date.now()
      })
    }
  },

  /**
   * 处理支付请求
   */
  handlePaymentRequest(config = {}) {
    console.log('处理支付请求:', config)
    
    // 这里需要根据实际的支付参数调用 wx.requestPayment
    // 示例中只是返回错误，实际使用时需要传入正确的支付参数
    this.postMessageToWebview({
      type: 'paymentResult',
      success: false,
      error: '支付功能需要配置具体的支付参数',
      timestamp: Date.now()
    })
  },

  /**
   * 向网页发送消息
   */
  postMessageToWebview(data) {
    console.log('向网页发送消息:', data)
    
    try {
      // 使用本地存储传递数据给网页
      wx.setStorageSync('webview_message', JSON.stringify(data))
      
      // 也可以尝试其他通信方式，比如通过URL参数等
      
    } catch (error) {
      console.error('发送消息到网页失败:', error)
    }
  },

  /**
   * 清理网页相关数据
   */
  clearWebviewData() {
    try {
      wx.removeStorageSync('webview_message')
      wx.removeStorageSync('webview_scan_result')
      wx.removeStorageSync('share_config')
    } catch (error) {
      console.error('清理数据失败:', error)
    }
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    try {
      const shareConfig = wx.getStorageSync('share_config') || {}
      
      return {
        title: shareConfig.title || '扫码应用',
        path: shareConfig.path || '/pages/webview/index',
        imageUrl: shareConfig.imageUrl || ''
      }
    } catch (error) {
      console.error('获取分享配置失败:', error)
      return {
        title: '扫码应用',
        path: '/pages/webview/index'
      }
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    try {
      const shareConfig = wx.getStorageSync('share_config') || {}
      
      return {
        title: shareConfig.title || '扫码应用',
        query: shareConfig.query || '',
        imageUrl: shareConfig.imageUrl || ''
      }
    } catch (error) {
      console.error('获取分享配置失败:', error)
      return {
        title: '扫码应用'
      }
    }
  }
})
