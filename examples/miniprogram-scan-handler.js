/**
 * 小程序页面示例：处理内嵌网页的扫码请求
 * 
 * 使用方法：
 * 1. 将此代码添加到您的小程序页面的 .js 文件中
 * 2. 在页面的 .wxml 文件中添加 <web-view> 组件
 * 3. 确保小程序已获得扫码权限
 */

Page({
  data: {
    // 网页URL
    webviewUrl: 'https://your-domain.com/your-page',
  },

  onLoad() {
    console.log('小程序页面加载完成')
  },

  /**
   * 处理网页发送的消息
   * @param {Object} e 消息事件对象
   */
  onMessage(e) {
    console.log('收到网页消息:', e.detail.data)
    
    const messages = e.detail.data
    if (!Array.isArray(messages) || messages.length === 0) {
      return
    }

    // 处理最新的消息
    const latestMessage = messages[messages.length - 1]
    this.handleWebviewMessage(latestMessage)
  },

  /**
   * 处理具体的网页消息
   * @param {Object} message 消息内容
   */
  handleWebviewMessage(message) {
    if (!message || !message.type) {
      return
    }

    switch (message.type) {
      case 'requestScan':
        this.handleScanRequest(message.config || {})
        break
      case 'requestLocation':
        this.handleLocationRequest(message.config || {})
        break
      case 'requestUserInfo':
        this.handleUserInfoRequest()
        break
      default:
        console.log('未知消息类型:', message.type)
    }
  },

  /**
   * 处理扫码请求
   * @param {Object} config 扫码配置
   */
  handleScanRequest(config = {}) {
    console.log('处理扫码请求:', config)

    // 调用小程序扫码API
    wx.scanCode({
      onlyFromCamera: config.onlyFromCamera !== false, // 默认只允许从相机扫码
      scanType: config.scanType || ['qrCode', 'barCode'], // 扫码类型
      success: (res) => {
        console.log('扫码成功:', res)
        
        // 向网页发送扫码结果
        this.postMessageToWebview({
          type: 'scanResult',
          success: true,
          result: res.result,
          scanType: res.scanType,
          charSet: res.charSet,
          path: res.path
        })
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        
        // 向网页发送扫码失败信息
        this.postMessageToWebview({
          type: 'scanResult',
          success: false,
          error: error.errMsg || '扫码失败'
        })
      }
    })
  },

  /**
   * 处理位置请求
   * @param {Object} config 位置配置
   */
  handleLocationRequest(config = {}) {
    console.log('处理位置请求:', config)

    wx.getLocation({
      type: config.type || 'wgs84',
      altitude: config.altitude || false,
      success: (res) => {
        console.log('获取位置成功:', res)
        
        this.postMessageToWebview({
          type: 'locationResult',
          success: true,
          latitude: res.latitude,
          longitude: res.longitude,
          speed: res.speed,
          accuracy: res.accuracy,
          altitude: res.altitude,
          verticalAccuracy: res.verticalAccuracy,
          horizontalAccuracy: res.horizontalAccuracy
        })
      },
      fail: (error) => {
        console.error('获取位置失败:', error)
        
        this.postMessageToWebview({
          type: 'locationResult',
          success: false,
          error: error.errMsg || '获取位置失败'
        })
      }
    })
  },

  /**
   * 处理用户信息请求
   */
  handleUserInfoRequest() {
    console.log('处理用户信息请求')

    // 获取用户信息需要用户授权，这里只是示例
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res)
        
        this.postMessageToWebview({
          type: 'userInfoResult',
          success: true,
          userInfo: res.userInfo,
          rawData: res.rawData,
          signature: res.signature,
          encryptedData: res.encryptedData,
          iv: res.iv
        })
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error)
        
        this.postMessageToWebview({
          type: 'userInfoResult',
          success: false,
          error: error.errMsg || '获取用户信息失败'
        })
      }
    })
  },

  /**
   * 向网页发送消息
   * @param {Object} data 要发送的数据
   */
  postMessageToWebview(data) {
    // 注意：小程序向网页发送消息需要通过特定的方式
    // 这里使用页面跳转的方式传递数据，实际项目中可能需要其他方案
    
    console.log('向网页发送消息:', data)
    
    // 方案1: 通过页面跳转传递数据（适用于简单场景）
    // 注意：这种方式会刷新网页，可能不是最佳方案
    
    // 方案2: 使用全局变量或本地存储
    // 网页可以通过轮询检查这些数据
    try {
      wx.setStorageSync('webview_message', JSON.stringify(data))
      
      // 触发网页检查存储的事件（如果网页有监听的话）
      // 这需要网页端配合实现轮询或其他检查机制
      
    } catch (error) {
      console.error('发送消息到网页失败:', error)
    }
  },

  /**
   * 页面显示时的处理
   */
  onShow() {
    console.log('小程序页面显示')
  },

  /**
   * 页面隐藏时的处理
   */
  onHide() {
    console.log('小程序页面隐藏')
  },

  /**
   * 页面卸载时的处理
   */
  onUnload() {
    console.log('小程序页面卸载')
    
    // 清理可能的定时器或监听器
    // ...
  }
})
