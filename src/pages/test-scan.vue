<template>
  <div class="test-scan-page p-4">
    <van-nav-bar title="扫码功能测试" left-arrow @click-left="$router.back()" />
    
    <div class="mt-4">
      <van-cell-group>
        <van-cell title="环境信息" />
        <van-cell title="当前环境" :value="environmentInfo.current" />
        <van-cell title="User Agent" :value="environmentInfo.userAgent" />
        <van-cell title="微信环境" :value="environmentInfo.isWechat ? '是' : '否'" />
        <van-cell title="小程序环境" :value="environmentInfo.isMiniProgram ? '是' : '否'" />
        <van-cell title="__wxjs_environment" :value="environmentInfo.wxjsEnvironment" />
      </van-cell-group>
    </div>

    <div class="mt-4">
      <van-cell-group>
        <van-cell title="扫码支持情况" />
        <van-cell title="微信 JS-SDK 扫码" :value="scanSupport.wechatScan ? '支持' : '不支持'" />
        <van-cell title="小程序扫码" :value="scanSupport.miniProgramScan ? '支持' : '不支持'" />
        <van-cell title="整体支持" :value="scanSupport.anyScan ? '支持' : '不支持'" />
      </van-cell-group>
    </div>

    <div class="mt-4">
      <van-cell-group>
        <van-cell title="扫码测试" />
      </van-cell-group>
      
      <div class="p-4 text-center space-y-2">
        <QrScanner
          button-text="测试扫码"
          button-type="primary"
          button-size="large"
          :on-success="handleScanSuccess"
          :on-error="handleScanError"
        />

        <!-- 进度显示 -->
        <div v-if="scanProgress" class="text-sm text-gray-600 mt-2">
          {{ scanProgress }}
        </div>

        <!-- 直接调用智能扫码 -->
        <van-button
          type="success"
          size="large"
          block
          :loading="directScanning"
          @click="testDirectScan"
        >
          直接测试智能扫码
        </van-button>
      </div>
    </div>

    <div class="mt-4">
      <van-cell-group>
        <van-cell title="扫码结果" />
      </van-cell-group>
      
      <div class="p-4">
        <van-field
          v-model="scanResult"
          type="textarea"
          placeholder="扫码结果将显示在这里"
          readonly
          rows="4"
        />
      </div>
    </div>

    <div class="mt-4">
      <van-cell-group>
        <van-cell title="测试工具" />
      </van-cell-group>
      
      <div class="p-4 space-y-2">
        <van-button 
          type="default" 
          block 
          @click="testPostMessage"
        >
          测试 PostMessage 通信
        </van-button>
        
        <van-button 
          type="default" 
          block 
          @click="testEnvironmentDetection"
        >
          重新检测环境
        </van-button>
        
        <van-button
          type="default"
          block
          @click="showLogs"
        >
          显示调试日志
        </van-button>

        <van-button
          type="default"
          block
          @click="checkUrlParams"
        >
          检查 URL 参数
        </van-button>
      </div>
    </div>

    <!-- 日志弹窗 -->
    <van-dialog
      v-model:show="showLogDialog"
      title="调试日志"
      :show-cancel-button="false"
      confirm-button-text="关闭"
    >
      <div class="log-content p-4 max-h-80 overflow-y-auto">
        <pre class="text-xs">{{ logs.join('\n') }}</pre>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { showSuccessToast, showFailToast } from 'vant'
import QrScanner from '@/components/QrScanner/index.vue'
import {
  isInMiniProgramWebview,
  canUseMiniProgramScan,
  smartScan,
  checkUrlForScanResult,
  watchUrlForScanResult
} from '@/utils/miniProgramScan'
import { isWechatBrowser, wechatSDK } from '@/utils/wechat'
import { useWechatSDK } from '@/store/wechat'

const wechatStore = useWechatSDK()
const scanResult = ref('')
const showLogDialog = ref(false)
const logs = ref<string[]>([])
const scanProgress = ref('')
const directScanning = ref(false)
let urlWatcher: (() => void) | null = null

// 环境信息
const environmentInfo = computed(() => ({
  current: isInMiniProgramWebview() ? '小程序 webview' : (isWechatBrowser() ? '微信' : '其他'),
  userAgent: navigator.userAgent,
  isWechat: isWechatBrowser(),
  isMiniProgram: isInMiniProgramWebview(),
  wxjsEnvironment: (window as any).__wxjs_environment || '未设置'
}))

// 扫码支持情况
const scanSupport = computed(() => ({
  wechatScan: wechatStore.canScan,
  miniProgramScan: canUseMiniProgramScan(),
  anyScan: wechatStore.canScan || canUseMiniProgramScan()
}))

// 添加日志
function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push(`[${timestamp}] ${message}`)
  console.log(message)
}

// 扫码成功处理
function handleScanSuccess(result: string, parsedResult?: any) {
  scanResult.value = result
  addLog(`扫码成功: ${result}`)
  if (parsedResult) {
    addLog(`解析结果: ${JSON.stringify(parsedResult, null, 2)}`)
  }
  showSuccessToast('扫码成功')
}

// 扫码失败处理
function handleScanError(error: Error) {
  addLog(`扫码失败: ${error.message}`)
  showFailToast(`扫码失败: ${error.message}`)
}

// 测试 PostMessage 通信
function testPostMessage() {
  addLog('开始测试 PostMessage 通信...')
  
  try {
    const wx = (window as any).wx
    if (wx && wx.miniProgram && wx.miniProgram.postMessage) {
      wx.miniProgram.postMessage({
        data: {
          type: 'test',
          message: 'Hello from webview',
          timestamp: Date.now()
        }
      })
      addLog('已通过 wx.miniProgram.postMessage 发送测试消息')
    } else if (window.parent && window.parent !== window) {
      window.parent.postMessage({
        type: 'test',
        message: 'Hello from webview',
        timestamp: Date.now()
      }, '*')
      addLog('已通过 window.parent.postMessage 发送测试消息')
    } else {
      addLog('无法找到可用的 PostMessage 通道')
    }
  } catch (error) {
    addLog(`PostMessage 测试失败: ${error}`)
  }
}

// 重新检测环境
function testEnvironmentDetection() {
  addLog('重新检测环境...')
  addLog(`User Agent: ${navigator.userAgent}`)
  addLog(`是否微信环境: ${isWechatBrowser()}`)
  addLog(`是否小程序环境: ${isInMiniProgramWebview()}`)
  addLog(`__wxjs_environment: ${(window as any).__wxjs_environment}`)
  addLog(`wx 对象存在: ${!!(window as any).wx}`)
  addLog(`微信扫码支持: ${wechatStore.canScan}`)
  addLog(`小程序扫码支持: ${canUseMiniProgramScan()}`)
}

// 显示日志
function showLogs() {
  showLogDialog.value = true
}

// 直接测试智能扫码
async function testDirectScan() {
  if (directScanning.value) return

  try {
    directScanning.value = true
    scanProgress.value = '正在初始化扫码...'
    addLog('开始直接测试智能扫码')

    const result = await smartScan({
      timeout: 30000,
      retryCount: 2,
      retryDelay: 1000,
      onProgress: (message) => {
        scanProgress.value = message
        addLog(`进度: ${message}`)
      },
      onSuccess: (scanResult) => {
        addLog(`扫码成功: ${scanResult}`)
      },
      onError: (error) => {
        addLog(`扫码失败: ${error}`)
      },
      onCancel: () => {
        addLog('用户取消扫码')
      }
    })

    if (result.success && result.result) {
      scanResult.value = result.result
      scanProgress.value = '扫码成功！'
      addLog(`智能扫码成功: ${result.result}`)
      showSuccessToast('扫码成功')
    } else {
      scanProgress.value = '扫码失败'
      addLog(`智能扫码失败: ${result.error}`)
      showFailToast(`扫码失败: ${result.error}`)
    }
  } catch (error) {
    scanProgress.value = '扫码异常'
    addLog(`智能扫码异常: ${error}`)
    showFailToast(`扫码异常: ${error}`)
  } finally {
    directScanning.value = false
    setTimeout(() => {
      scanProgress.value = ''
    }, 3000)
  }
}

// 检查 URL 参数
function checkUrlParams() {
  addLog('检查 URL 参数...')

  const result = checkUrlForScanResult()
  if (result) {
    addLog(`从 URL 参数获取到扫码结果: ${JSON.stringify(result)}`)
    if (result.success && result.result) {
      handleScanSuccess(result.result)
    } else {
      handleScanError(new Error(result.error || '扫码失败'))
    }
  } else {
    addLog('URL 参数中没有扫码结果')
  }

  // 显示当前 URL 信息
  addLog(`当前 URL: ${window.location.href}`)
  addLog(`URL 参数: ${window.location.search}`)
}

// 启动 URL 监听
function startUrlWatcher() {
  if (urlWatcher) {
    urlWatcher()
  }

  urlWatcher = watchUrlForScanResult((result) => {
    addLog(`URL 监听到扫码结果: ${JSON.stringify(result)}`)
    if (result.success && result.result) {
      handleScanSuccess(result.result)
    } else {
      handleScanError(new Error(result.error || '扫码失败'))
    }
  })

  addLog('已启动 URL 监听')
}

// 停止 URL 监听
function stopUrlWatcher() {
  if (urlWatcher) {
    urlWatcher()
    urlWatcher = null
    addLog('已停止 URL 监听')
  }
}

// 监听小程序消息
function setupMessageListener() {
  window.addEventListener('message', (event) => {
    addLog(`收到消息: ${JSON.stringify(event.data)}`)
  })
}

onMounted(() => {
  addLog('页面加载完成')
  testEnvironmentDetection()
  setupMessageListener()

  // 检查初始 URL 参数
  checkUrlParams()

  // 启动 URL 监听
  startUrlWatcher()
})

// 页面卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
  stopUrlWatcher()
})
</script>

<style scoped>
.test-scan-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.log-content pre {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
