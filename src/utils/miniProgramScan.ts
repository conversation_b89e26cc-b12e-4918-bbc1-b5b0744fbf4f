/**
 * 小程序 webview 扫码功能
 * 通过 postMessage 与小程序通信实现扫码
 */
import { showFailToast } from 'vant'

export interface MiniProgramScanResult {
  success: boolean
  result?: string
  error?: string
}

export interface MiniProgramScanConfig {
  timeout?: number // 超时时间，默认30秒
  onSuccess?: (result: string) => void
  onError?: (error: string) => void
  onCancel?: () => void
}

/**
 * 检查是否在小程序 webview 环境中
 */
export function isInMiniProgramWebview(): boolean {
  const ua = navigator.userAgent.toLowerCase()
  return /miniprogram/i.test(ua) || (window as any).__wxjs_environment === 'miniprogram'
}

/**
 * 检查小程序扫码功能是否可用
 */
export function canUseMiniProgramScan(): boolean {
  return isInMiniProgramWebview() && typeof (window as any).wx !== 'undefined'
}

/**
 * 通过小程序 postMessage 实现扫码
 */
export function scanWithMiniProgram(config: MiniProgramScanConfig = {}): Promise<MiniProgramScanResult> {
  return new Promise((resolve) => {
    const timeout = config.timeout || 30000 // 默认30秒超时
    let isResolved = false

    // 超时处理
    const timeoutId = setTimeout(() => {
      if (!isResolved) {
        isResolved = true
        const error = '扫码超时'
        config.onError?.(error)
        resolve({ success: false, error })
      }
    }, timeout)

    // 监听小程序返回的扫码结果
    const handleMessage = (event: MessageEvent) => {
      if (isResolved) return

      try {
        const data = event.data
        console.log('收到小程序消息:', data)

        // 检查是否是扫码结果
        if (data && data.type === 'scanResult') {
          isResolved = true
          clearTimeout(timeoutId)
          window.removeEventListener('message', handleMessage)

          if (data.success) {
            config.onSuccess?.(data.result)
            resolve({ success: true, result: data.result })
          } else {
            const error = data.error || '扫码失败'
            config.onError?.(error)
            resolve({ success: false, error })
          }
        } else if (data && data.type === 'scanCancel') {
          isResolved = true
          clearTimeout(timeoutId)
          window.removeEventListener('message', handleMessage)
          config.onCancel?.()
          resolve({ success: false, error: '用户取消扫码' })
        }
      } catch (error) {
        console.error('处理小程序消息失败:', error)
      }
    }

    // 监听小程序消息
    window.addEventListener('message', handleMessage)

    try {
      // 通过 postMessage 请求小程序扫码
      const wx = (window as any).wx
      if (wx && wx.miniProgram && wx.miniProgram.postMessage) {
        wx.miniProgram.postMessage({
          data: {
            type: 'requestScan',
            timestamp: Date.now()
          }
        })
        console.log('已发送扫码请求到小程序')
      } else {
        // 尝试直接通过 parent 发送消息
        if (window.parent && window.parent !== window) {
          window.parent.postMessage({
            type: 'requestScan',
            timestamp: Date.now()
          }, '*')
          console.log('已通过 parent 发送扫码请求')
        } else {
          isResolved = true
          clearTimeout(timeoutId)
          window.removeEventListener('message', handleMessage)
          const error = '无法与小程序通信'
          config.onError?.(error)
          resolve({ success: false, error })
        }
      }
    } catch (error) {
      isResolved = true
      clearTimeout(timeoutId)
      window.removeEventListener('message', handleMessage)
      const errorMsg = `发送扫码请求失败: ${error}`
      config.onError?.(errorMsg)
      resolve({ success: false, error: errorMsg })
    }
  })
}

/**
 * 尝试使用微信 JS-SDK 在小程序环境中扫码
 * 注意：这个方法在小程序 webview 中可能不可用
 */
export function scanWithWxJSSDKInMiniProgram(): Promise<MiniProgramScanResult> {
  return new Promise((resolve) => {
    try {
      const wx = (window as any).wx
      if (!wx || !wx.scanQRCode) {
        resolve({ success: false, error: '微信 JS-SDK 扫码功能不可用' })
        return
      }

      wx.scanQRCode({
        needResult: 1,
        scanType: ['qrCode', 'barCode'],
        success: (res: any) => {
          console.log('微信 JS-SDK 扫码成功:', res)
          resolve({ success: true, result: res.resultStr })
        },
        fail: (error: any) => {
          console.error('微信 JS-SDK 扫码失败:', error)
          let errorMsg = '扫码失败'
          
          if (error.errMsg) {
            if (error.errMsg.includes('permission denied')) {
              errorMsg = '扫码权限被拒绝'
            } else if (error.errMsg.includes('cancel')) {
              errorMsg = '用户取消扫码'
            } else {
              errorMsg = error.errMsg
            }
          }
          
          resolve({ success: false, error: errorMsg })
        }
      })
    } catch (error) {
      resolve({ success: false, error: `扫码异常: ${error}` })
    }
  })
}

/**
 * 智能扫码：自动选择最佳的扫码方式
 */
export async function smartScan(config: MiniProgramScanConfig = {}): Promise<MiniProgramScanResult> {
  console.log('开始智能扫码...')
  
  // 检查环境
  if (!isInMiniProgramWebview()) {
    return { success: false, error: '当前不在小程序 webview 环境中' }
  }

  // 方案1：尝试通过 postMessage 与小程序通信
  console.log('尝试通过 postMessage 扫码...')
  try {
    const result = await scanWithMiniProgram(config)
    if (result.success) {
      return result
    }
    console.log('postMessage 扫码失败:', result.error)
  } catch (error) {
    console.log('postMessage 扫码异常:', error)
  }

  // 方案2：尝试使用微信 JS-SDK（在小程序环境中可能受限）
  console.log('尝试使用微信 JS-SDK 扫码...')
  try {
    const result = await scanWithWxJSSDKInMiniProgram()
    if (result.success) {
      return result
    }
    console.log('微信 JS-SDK 扫码失败:', result.error)
  } catch (error) {
    console.log('微信 JS-SDK 扫码异常:', error)
  }

  // 所有方案都失败
  return { 
    success: false, 
    error: '小程序 webview 环境中扫码功能不可用，请联系开发者配置小程序扫码支持' 
  }
}

/**
 * 显示小程序扫码使用说明
 */
export function showMiniProgramScanGuide() {
  const message = `
小程序 webview 扫码功能需要小程序端配置支持：

1. 小程序需要监听 webview 的 postMessage 事件
2. 收到 requestScan 消息时调用小程序扫码 API
3. 将扫码结果通过 postMessage 返回给 webview

请联系小程序开发者进行配置。
  `.trim()
  
  console.log(message)
  showFailToast('扫码功能需要小程序端支持')
}

/**
 * 获取小程序扫码配置建议
 */
export function getMiniProgramScanSetupGuide(): string {
  return `
# 小程序端扫码配置指南

## uniapp 配置方式

### 1. 页面模板
\`\`\`vue
<template>
  <view class="webview-container">
    <web-view
      ref="webview"
      :src="webviewUrl"
      @message="onWebviewMessage">
    </web-view>
  </view>
</template>
\`\`\`

### 2. 页面逻辑
\`\`\`javascript
export default {
  data() {
    return {
      webviewUrl: 'https://your-domain.com/your-page'
    }
  },

  methods: {
    onWebviewMessage(event) {
      if (event.detail.data && event.detail.data.length > 0) {
        const data = event.detail.data[event.detail.data.length - 1]
        if (data.type === 'requestScan') {
          this.handleScanRequest()
        }
      }
    },

    handleScanRequest() {
      uni.scanCode({
        success: (res) => {
          this.sendMessageToWebview({
            type: 'scanResult',
            success: true,
            result: res.result
          })
        },
        fail: (error) => {
          this.sendMessageToWebview({
            type: 'scanResult',
            success: false,
            error: error.errMsg || '扫码失败'
          })
        }
      })
    },

    sendMessageToWebview(data) {
      const webview = this.$refs.webview
      if (webview) {
        webview.postMessage({ data })
      }
    }
  }
}
\`\`\`

## 原生小程序配置方式

### 1. 页面模板
\`\`\`xml
<web-view id="webview" src="{{webviewUrl}}" bindmessage="onWebviewMessage"></web-view>
\`\`\`

### 2. 页面逻辑
\`\`\`javascript
Page({
  onLoad() {
    wx.onMessage && wx.onMessage((data) => {
      if (data.type === 'requestScan') {
        this.handleScanRequest()
      }
    })
  },

  handleScanRequest() {
    wx.scanCode({
      success: (res) => {
        this.sendMessageToWebview({
          type: 'scanResult',
          success: true,
          result: res.result
        })
      },
      fail: (error) => {
        this.sendMessageToWebview({
          type: 'scanResult',
          success: false,
          error: error.errMsg || '扫码失败'
        })
      }
    })
  },

  sendMessageToWebview(data) {
    const webview = this.selectComponent('#webview')
    if (webview) {
      webview.postMessage({ data })
    }
  }
})
\`\`\`

详细配置请参考：docs/uniapp-scan-quick-setup.md
  `.trim()
}
