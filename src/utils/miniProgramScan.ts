/**
 * 小程序 webview 扫码功能
 * 通过 postMessage 与小程序通信实现扫码
 */
import { showFailToast } from 'vant'

export interface MiniProgramScanResult {
  success: boolean
  result?: string
  error?: string
}

export interface MiniProgramScanConfig {
  timeout?: number // 超时时间，默认30秒
  retryCount?: number // 重试次数，默认2次
  retryDelay?: number // 重试延迟，默认1秒
  onSuccess?: (result: string) => void
  onError?: (error: string) => void
  onCancel?: () => void
  onProgress?: (message: string) => void // 进度回调
}

/**
 * 检查是否在小程序 webview 环境中
 */
export function isInMiniProgramWebview(): boolean {
  const ua = navigator.userAgent.toLowerCase()

  // 检查多种小程序环境标识
  const isMiniProgram = /miniprogram/i.test(ua) ||
                       (window as any).__wxjs_environment === 'miniprogram' ||
                       /swan/i.test(ua) || // 百度小程序
                       /alipay/i.test(ua) || // 支付宝小程序
                       /toutiaomicroapp/i.test(ua) // 字节跳动小程序

  // 额外检查微信小程序特有的标识
  const isWechatMiniProgram = /micromessenger/i.test(ua) && /miniprogram/i.test(ua)

  console.log('小程序环境检测:', {
    userAgent: ua,
    wxjsEnvironment: (window as any).__wxjs_environment,
    isMiniProgram,
    isWechatMiniProgram
  })

  return isMiniProgram
}

/**
 * 检查小程序扫码功能是否可用
 */
export function canUseMiniProgramScan(): boolean {
  const isInMiniProgram = isInMiniProgramWebview()
  const hasWxObject = typeof (window as any).wx !== 'undefined'
  const hasMiniProgramAPI = hasWxObject && (window as any).wx.miniProgram
  const hasPostMessage = hasMiniProgramAPI && (window as any).wx.miniProgram.postMessage

  console.log('小程序扫码功能检测:', {
    isInMiniProgram,
    hasWxObject,
    hasMiniProgramAPI,
    hasPostMessage,
    canScan: isInMiniProgram && hasWxObject
  })

  return isInMiniProgram && hasWxObject
}

/**
 * 确保微信 JSSDK 已加载
 */
export function ensureWxSDKLoaded(): Promise<boolean> {
  return new Promise((resolve) => {
    // 如果已经存在 wx 对象，直接返回
    if (typeof (window as any).wx !== 'undefined') {
      resolve(true)
      return
    }

    // 动态加载微信 JSSDK
    const script = document.createElement('script')
    script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'
    script.async = true

    script.onload = () => {
      console.log('微信 JSSDK 加载成功')
      resolve(true)
    }

    script.onerror = () => {
      console.error('微信 JSSDK 加载失败')
      resolve(false)
    }

    document.head.appendChild(script)
  })
}

/**
 * 通过小程序 postMessage 实现扫码（带重试机制）
 */
export async function scanWithMiniProgram(config: MiniProgramScanConfig = {}): Promise<MiniProgramScanResult> {
  const maxRetries = config.retryCount || 2
  const retryDelay = config.retryDelay || 1000

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      config.onProgress?.(`尝试扫码 (${attempt + 1}/${maxRetries + 1})`)
      const result = await _scanWithMiniProgramOnce(config)

      if (result.success) {
        return result
      }

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        config.onProgress?.(`扫码失败，${retryDelay / 1000}秒后重试...`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      } else {
        return result
      }
    } catch (error) {
      if (attempt === maxRetries) {
        return { success: false, error: `扫码失败: ${error}` }
      }
    }
  }

  return { success: false, error: '扫码失败，已达到最大重试次数' }
}

/**
 * 单次扫码尝试
 */
async function _scanWithMiniProgramOnce(config: MiniProgramScanConfig = {}): Promise<MiniProgramScanResult> {
  // 确保 JSSDK 已加载
  await ensureWxSDKLoaded()

  return new Promise((resolve) => {
    const timeout = config.timeout || 30000 // 默认30秒超时
    let isResolved = false

    // 超时处理
    const timeoutId = setTimeout(() => {
      if (!isResolved) {
        isResolved = true
        const error = '扫码超时'
        config.onError?.(error)
        resolve({ success: false, error })
      }
    }, timeout)

    // 监听小程序返回的扫码结果
    const handleMessage = (event: MessageEvent) => {
      if (isResolved) return

      try {
        const data = event.data
        console.log('收到小程序消息:', data)

        // 检查是否是扫码结果
        if (data && data.type === 'scanResult') {
          isResolved = true
          clearTimeout(timeoutId)
          window.removeEventListener('message', handleMessage)

          if (data.success) {
            config.onSuccess?.(data.result)
            resolve({ success: true, result: data.result })
          } else {
            const error = data.error || '扫码失败'
            config.onError?.(error)
            resolve({ success: false, error })
          }
        } else if (data && data.type === 'scanCancel') {
          isResolved = true
          clearTimeout(timeoutId)
          window.removeEventListener('message', handleMessage)
          config.onCancel?.()
          resolve({ success: false, error: '用户取消扫码' })
        }
      } catch (error) {
        console.error('处理小程序消息失败:', error)
      }
    }

    // 监听小程序消息
    window.addEventListener('message', handleMessage)

    try {
      // 通过 postMessage 请求小程序扫码
      const wx = (window as any).wx
      const scanRequest = {
        type: 'requestScan',
        timestamp: Date.now(),
        requestId: Math.random().toString(36).substring(2, 11) // 生成请求ID
      }

      console.log('准备发送扫码请求:', scanRequest)
      console.log('wx 对象状态:', {
        hasWx: !!wx,
        hasMiniProgram: !!(wx && wx.miniProgram),
        hasPostMessage: !!(wx && wx.miniProgram && wx.miniProgram.postMessage)
      })

      if (wx && wx.miniProgram && wx.miniProgram.postMessage) {
        wx.miniProgram.postMessage({ data: scanRequest })
        console.log('✅ 已通过 wx.miniProgram.postMessage 发送扫码请求')
        config.onProgress?.('已发送扫码请求到小程序')
      } else if (window.parent && window.parent !== window) {
        // 备用方案：直接通过 parent 发送消息
        window.parent.postMessage(scanRequest, '*')
        console.log('✅ 已通过 window.parent.postMessage 发送扫码请求')
        config.onProgress?.('已通过备用方式发送扫码请求')
      } else {
        isResolved = true
        clearTimeout(timeoutId)
        window.removeEventListener('message', handleMessage)
        const error = '无法与小程序通信：未找到可用的通信方式'
        console.error('❌', error)
        config.onError?.(error)
        resolve({ success: false, error })
        return
      }

      // 发送成功后，等待小程序响应
      config.onProgress?.('等待小程序响应...')

    } catch (error) {
      isResolved = true
      clearTimeout(timeoutId)
      window.removeEventListener('message', handleMessage)
      const errorMsg = `发送扫码请求失败: ${error}`
      console.error('❌', errorMsg)
      config.onError?.(errorMsg)
      resolve({ success: false, error: errorMsg })
    }
  })
}

/**
 * 尝试使用微信 JS-SDK 在小程序环境中扫码
 * 注意：这个方法在小程序 webview 中可能不可用
 */
export function scanWithWxJSSDKInMiniProgram(): Promise<MiniProgramScanResult> {
  return new Promise((resolve) => {
    try {
      const wx = (window as any).wx
      if (!wx || !wx.scanQRCode) {
        resolve({ success: false, error: '微信 JS-SDK 扫码功能不可用' })
        return
      }

      wx.scanQRCode({
        needResult: 1,
        scanType: ['qrCode', 'barCode'],
        success: (res: any) => {
          console.log('微信 JS-SDK 扫码成功:', res)
          resolve({ success: true, result: res.resultStr })
        },
        fail: (error: any) => {
          console.error('微信 JS-SDK 扫码失败:', error)
          let errorMsg = '扫码失败'
          
          if (error.errMsg) {
            if (error.errMsg.includes('permission denied')) {
              errorMsg = '扫码权限被拒绝'
            } else if (error.errMsg.includes('cancel')) {
              errorMsg = '用户取消扫码'
            } else {
              errorMsg = error.errMsg
            }
          }
          
          resolve({ success: false, error: errorMsg })
        }
      })
    } catch (error) {
      resolve({ success: false, error: `扫码异常: ${error}` })
    }
  })
}

/**
 * 智能扫码：自动选择最佳的扫码方式
 */
export async function smartScan(config: MiniProgramScanConfig = {}): Promise<MiniProgramScanResult> {
  console.log('🚀 开始智能扫码...')

  // 检查环境
  if (!isInMiniProgramWebview()) {
    const error = '当前不在小程序 webview 环境中'
    console.error('❌', error)
    return { success: false, error }
  }

  // 检查基础条件
  const canScan = canUseMiniProgramScan()
  if (!canScan) {
    const error = '小程序扫码功能不可用：缺少必要的 API 支持'
    console.error('❌', error)
    return { success: false, error }
  }

  config.onProgress?.('正在初始化扫码功能...')

  // 首先检查 URL 参数中是否已有扫码结果
  const urlResult = checkUrlForScanResult()
  if (urlResult) {
    console.log('✅ 从 URL 参数获取到扫码结果:', urlResult)
    if (urlResult.success && urlResult.result) {
      config.onSuccess?.(urlResult.result)
    } else {
      config.onError?.(urlResult.error || '扫码失败')
    }
    return urlResult
  }

  // 方案1：尝试通过 postMessage 与小程序通信（推荐方式）
  console.log('📱 尝试通过 postMessage 扫码...')
  try {
    // 设置 URL 监听
    const stopWatching = watchUrlForScanResult((result) => {
      console.log('🔍 URL 监听到扫码结果:', result)
      if (result.success && result.result) {
        config.onSuccess?.(result.result)
      } else {
        config.onError?.(result.error || '扫码失败')
      }
    })

    // 尝试 postMessage 方式
    const result = await scanWithMiniProgram({
      ...config,
      onProgress: (msg) => {
        console.log('📱', msg)
        config.onProgress?.(msg)
      }
    })

    // 停止 URL 监听
    stopWatching()

    if (result.success) {
      console.log('✅ postMessage 扫码成功:', result.result)
      return result
    }
    console.log('⚠️ postMessage 扫码失败:', result.error)
  } catch (error) {
    console.log('❌ postMessage 扫码异常:', error)
  }

  // 方案2：尝试使用微信 JS-SDK（在小程序环境中可能受限）
  console.log('🔧 尝试使用微信 JS-SDK 扫码...')
  config.onProgress?.('尝试备用扫码方式...')

  try {
    const result = await scanWithWxJSSDKInMiniProgram()
    if (result.success) {
      console.log('✅ 微信 JS-SDK 扫码成功:', result.result)
      return result
    }
    console.log('⚠️ 微信 JS-SDK 扫码失败:', result.error)
  } catch (error) {
    console.log('❌ 微信 JS-SDK 扫码异常:', error)
  }

  // 所有方案都失败
  const finalError = '小程序 webview 环境中扫码功能不可用，请联系开发者配置小程序扫码支持'
  console.error('❌', finalError)
  config.onError?.(finalError)

  return {
    success: false,
    error: finalError
  }
}

/**
 * 显示小程序扫码使用说明
 */
export function showMiniProgramScanGuide() {
  const message = `
小程序 webview 扫码功能需要小程序端配置支持：

1. 小程序需要监听 webview 的 postMessage 事件
2. 收到 requestScan 消息时调用小程序扫码 API
3. 将扫码结果通过 postMessage 返回给 webview

请联系小程序开发者进行配置。
  `.trim()
  
  console.log(message)
  showFailToast('扫码功能需要小程序端支持')
}

/**
 * 检查 URL 参数中的扫码结果
 */
export function checkUrlForScanResult(): MiniProgramScanResult | null {
  try {
    const urlParams = new URLSearchParams(window.location.search)
    const scanData = urlParams.get('scanData')

    if (scanData) {
      const data = JSON.parse(decodeURIComponent(scanData))
      console.log('从 URL 参数获取到扫码数据:', data)

      // 清除 URL 参数，避免重复处理
      const newUrl = window.location.pathname + window.location.hash
      window.history.replaceState({}, '', newUrl)

      if (data.type === 'scanResult') {
        return {
          success: data.success,
          result: data.result,
          error: data.error
        }
      } else if (data.type === 'scanCancel') {
        return {
          success: false,
          error: '用户取消扫码'
        }
      }
    }
  } catch (error) {
    console.error('解析 URL 扫码结果失败:', error)
  }

  return null
}

/**
 * 监听 URL 变化中的扫码结果
 */
export function watchUrlForScanResult(callback: (result: MiniProgramScanResult) => void): () => void {
  let lastUrl = window.location.href

  const checkUrl = () => {
    const currentUrl = window.location.href
    if (currentUrl !== lastUrl) {
      lastUrl = currentUrl
      const result = checkUrlForScanResult()
      if (result) {
        callback(result)
      }
    }
  }

  // 监听 URL 变化
  window.addEventListener('popstate', checkUrl)

  // 定期检查 URL 变化（处理 pushState/replaceState）
  const intervalId = setInterval(checkUrl, 500)

  // 返回清理函数
  return () => {
    window.removeEventListener('popstate', checkUrl)
    clearInterval(intervalId)
  }
}

/**
 * 获取小程序扫码配置建议
 */
export function getMiniProgramScanSetupGuide(): string {
  return `
# 小程序端扫码配置指南

## uniapp 配置方式

### 1. 页面模板
\`\`\`vue
<template>
  <view class="webview-container">
    <web-view
      ref="webview"
      :src="webviewUrl"
      @message="onWebviewMessage">
    </web-view>
  </view>
</template>
\`\`\`

### 2. 页面逻辑
\`\`\`javascript
export default {
  data() {
    return {
      webviewUrl: 'https://your-domain.com/your-page'
    }
  },

  methods: {
    onWebviewMessage(event) {
      if (event.detail.data && event.detail.data.length > 0) {
        const data = event.detail.data[event.detail.data.length - 1]
        if (data.type === 'requestScan') {
          this.handleScanRequest()
        }
      }
    },

    handleScanRequest() {
      uni.scanCode({
        success: (res) => {
          this.sendMessageToWebview({
            type: 'scanResult',
            success: true,
            result: res.result
          })
        },
        fail: (error) => {
          this.sendMessageToWebview({
            type: 'scanResult',
            success: false,
            error: error.errMsg || '扫码失败'
          })
        }
      })
    },

    sendMessageToWebview(data) {
      const webview = this.$refs.webview
      if (webview) {
        webview.postMessage({ data })
      }
    }
  }
}
\`\`\`

## 原生小程序配置方式

### 1. 页面模板
\`\`\`xml
<web-view id="webview" src="{{webviewUrl}}" bindmessage="onWebviewMessage"></web-view>
\`\`\`

### 2. 页面逻辑
\`\`\`javascript
Page({
  onLoad() {
    wx.onMessage && wx.onMessage((data) => {
      if (data.type === 'requestScan') {
        this.handleScanRequest()
      }
    })
  },

  handleScanRequest() {
    wx.scanCode({
      success: (res) => {
        this.sendMessageToWebview({
          type: 'scanResult',
          success: true,
          result: res.result
        })
      },
      fail: (error) => {
        this.sendMessageToWebview({
          type: 'scanResult',
          success: false,
          error: error.errMsg || '扫码失败'
        })
      }
    })
  },

  sendMessageToWebview(data) {
    const webview = this.selectComponent('#webview')
    if (webview) {
      webview.postMessage({ data })
    }
  }
})
\`\`\`

详细配置请参考：docs/uniapp-scan-quick-setup.md
  `.trim()
}
